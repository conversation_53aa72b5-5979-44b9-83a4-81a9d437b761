; AutoHotkey v2 脚本 - 简单网页生成器
; 描述：使用AutoHotkey创建和显示简单的HTML网页
; 作者：AutoHotkey示例
; 版本：1.0
; 编码：UTF-8

#Requires AutoHotkey v2.0
#SingleInstance Force

; 设置文件编码处理
SetWorkingDir(A_ScriptDir)

; ===========================================
; 全局变量定义
; ===========================================
global 主窗口
global 网页内容编辑框
global 预览按钮
global 保存按钮
global 打开按钮
global 状态栏
global 当前文件路径 := ""

; ===========================================
; 主程序入口
; ===========================================
main()

; 主函数
main() {
    创建主界面()
    显示默认内容()
}

; ===========================================
; GUI界面创建
; ===========================================
创建主界面() {
    ; 创建主窗口
    global 主窗口 := Gui("+Resize", "简单网页生成器 - AutoHotkey")
    主窗口.MarginX := 10
    主窗口.MarginY := 10
    
    ; 添加标题
    主窗口.Add("Text", "x10 y10 w400 Center", "AutoHotkey 简单网页生成器").SetFont("s14 Bold")
    
    ; 添加说明文字
    主窗口.Add("Text", "x10 y40 w400", "在下方编辑框中输入HTML代码，然后点击预览按钮查看效果：")
    
    ; 创建HTML内容编辑框
    global 网页内容编辑框 := 主窗口.Add("Edit", "x10 y70 w400 h200 VScroll +WantReturn")
    
    ; 创建按钮区域
    global 预览按钮 := 主窗口.Add("Button", "x10 y280 w80 h30", "预览网页")
    global 保存按钮 := 主窗口.Add("Button", "x100 y280 w80 h30", "保存HTML")
    global 打开按钮 := 主窗口.Add("Button", "x190 y280 w80 h30", "打开文件")
    清空按钮 := 主窗口.Add("Button", "x280 y280 w80 h30", "清空内容")
    帮助按钮 := 主窗口.Add("Button", "x370 y280 w40 h30", "帮助")
    
    ; 创建状态栏
    global 状态栏 := 主窗口.Add("Text", "x10 y320 w400 h20 Border", "就绪")
    
    ; 绑定事件
    预览按钮.OnEvent("Click", 预览网页)
    保存按钮.OnEvent("Click", 保存HTML文件)
    打开按钮.OnEvent("Click", 打开HTML文件)
    清空按钮.OnEvent("Click", 清空编辑框)
    帮助按钮.OnEvent("Click", 显示帮助)
    
    ; 窗口事件
    主窗口.OnEvent("Close", 退出程序)
    主窗口.OnEvent("Size", 调整窗口大小)
    
    ; 显示窗口
    主窗口.Show("w420 h350")
}

; ===========================================
; 默认内容和模板
; ===========================================
显示默认内容() {
    默认HTML := '
    (
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的第一个网页</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            text-align: center;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        .button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>欢迎来到我的网页！</h1>
        <p>这是使用 AutoHotkey 创建的简单网页示例。</p>

        <h2>功能特点：</h2>
        <ul>
            <li>响应式设计</li>
            <li>现代化样式</li>
            <li>渐变背景</li>
            <li>毛玻璃效果</li>
        </ul>

        <h2>交互按钮：</h2>
        <button class="button" onclick="alert('Hello from AutoHotkey!')">点击我</button>
        <button class="button" onclick="changeColor()">改变颜色</button>

        <h2>当前时间：</h2>
        <p id="time"></p>
    </div>

    <script>
        function updateTime() {
            const now = new Date();
            document.getElementById('time').textContent = now.toLocaleString('zh-CN');
        }

        function changeColor() {
            const colors = [
                'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
            ];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            document.body.style.background = randomColor;
        }

        setInterval(updateTime, 1000);
        updateTime();
    </script>
</body>
</html>
    )`''
    
    网页内容编辑框.Text := 默认HTML
    状态栏.Text := "已加载默认模板"
}

; ===========================================
; 事件处理函数
; ===========================================

; 预览网页
预览网页(*) {
    try {
        ; 获取HTML内容
        html内容 := 网页内容编辑框.Text
        
        if (html内容 = "") {
            MsgBox("请先输入HTML内容！", "提示", "OK Icon!")
            return
        }
        
        ; 创建临时HTML文件
        临时文件 := A_ScriptDir . "\preview_temp.html"

        ; 写入文件
        try {
            if FileExist(临时文件) {
                FileDelete(临时文件)  ; 删除可能存在的旧文件
            }
            FileAppend(html内容, 临时文件, "UTF-8")
        } catch Error as fileError {
            throw Error("无法创建临时文件: " . fileError.Message)
        }

        ; 检查文件是否创建成功
        if !FileExist(临时文件) {
            throw Error("临时文件创建失败")
        }

        ; 在默认浏览器中打开
        Run(临时文件)
        
        状态栏.Text := "网页预览已打开"
        
    } catch Error as e {
        MsgBox("预览失败：" . e.Message, "错误", "OK Icon!")
        状态栏.Text := "预览失败"
    }
}

; 保存HTML文件
保存HTML文件(*) {
    try {
        html内容 := 网页内容编辑框.Text
        
        if (html内容 = "") {
            MsgBox("没有内容可保存！", "提示", "OK Icon!")
            return
        }
        
        ; 选择保存位置
        保存路径 := FileSelect("S", , "保存HTML文件", "HTML文件 (*.html)")
        
        if (保存路径 = "") {
            状态栏.Text := "保存已取消"
            return
        }
        
        ; 确保文件扩展名
        if (!RegExMatch(保存路径, "\.html?$", &match)) {
            保存路径 .= ".html"
        }
        
        ; 保存文件
        try {
            if FileExist(保存路径) {
                FileDelete(保存路径)  ; 删除可能存在的文件
            }
            FileAppend(html内容, 保存路径, "UTF-8")
        } catch Error as fileError {
            throw Error("无法保存文件: " . fileError.Message)
        }
        
        global 当前文件路径 := 保存路径
        状态栏.Text := "文件已保存：" . 保存路径
        
        ; 询问是否打开文件
        result := MsgBox("文件保存成功！是否在浏览器中打开？", "保存成功", "YesNo Icon?")
        if (result = "Yes") {
            Run(保存路径)
        }
        
    } catch Error as e {
        MsgBox("保存失败：" . e.Message, "错误", "OK Icon!")
        状态栏.Text := "保存失败"
    }
}

; 打开HTML文件
打开HTML文件(*) {
    try {
        ; 选择要打开的文件
        文件路径 := FileSelect(1, , "打开HTML文件", "HTML文件 (*.html)")
        
        if (文件路径 = "") {
            状态栏.Text := "打开已取消"
            return
        }
        
        ; 读取文件内容
        文件内容 := FileRead(文件路径, "UTF-8")
        网页内容编辑框.Text := 文件内容
        
        global 当前文件路径 := 文件路径
        状态栏.Text := "文件已打开：" . 文件路径
        
    } catch Error as e {
        MsgBox("打开文件失败：" . e.Message, "错误", "OK Icon!")
        状态栏.Text := "打开文件失败"
    }
}

; 清空编辑框
清空编辑框(*) {
    result := MsgBox("确定要清空所有内容吗？", "确认", "YesNo Icon?")
    if (result = "Yes") {
        网页内容编辑框.Text := ""
        状态栏.Text := "内容已清空"
    }
}

; 显示帮助
显示帮助(*) {
    帮助内容 := "
    (
    AutoHotkey 简单网页生成器 - 使用帮助
    
    功能说明：
    • 预览网页：在默认浏览器中预览当前HTML代码
    • 保存HTML：将HTML代码保存为.html文件
    • 打开文件：打开现有的HTML文件进行编辑
    • 清空内容：清空编辑框中的所有内容
    
    使用技巧：
    1. 可以直接在编辑框中输入HTML代码
    2. 支持完整的HTML、CSS和JavaScript
    3. 预览功能会创建临时文件并在浏览器中打开
    4. 保存的文件可以在任何浏览器中正常显示
    
    快捷键：
    • Ctrl+S：快速保存（如果已设置文件路径）
    • F5：快速预览
    • Ctrl+N：清空内容
    
    版本：1.0
    )"
    
    MsgBox(帮助内容, "使用帮助", "OK")
}

; 调整窗口大小
调整窗口大小(gui对象, 最小最大, 宽度, 高度) {
    if (最小最大 = -1)  ; 最小化时不处理
        return
        
    ; 调整编辑框大小
    网页内容编辑框.Move(, , 宽度 - 20, 高度 - 150)
    
    ; 调整按钮位置
    预览按钮.Move(, 高度 - 70)
    保存按钮.Move(, 高度 - 70)
    打开按钮.Move(, 高度 - 70)
    
    ; 调整状态栏
    状态栏.Move(, 高度 - 30, 宽度 - 20)
}

; 退出程序
退出程序(*) {
    ExitApp()
}

; ===========================================
; 快捷键定义
; ===========================================
#HotIf WinActive("简单网页生成器")
^s::保存HTML文件()  ; Ctrl+S 保存
F5::预览网页()       ; F5 预览
^n::清空编辑框()     ; Ctrl+N 清空
#HotIf
