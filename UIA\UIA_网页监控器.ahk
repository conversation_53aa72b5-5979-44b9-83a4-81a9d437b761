#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk
#include ..\UIA-v2-1.1.0\Lib\UIA_Browser.ahk

/*
UIA 网页变化监控器
专门监控浏览器网页的变化
功能：
1. 监控页面标题变化
2. 监控URL变化
3. 监控页面元素变化
4. 监控加载状态
*/

; 全局变量
global 监控窗口
global 日志区域
global 页面信息区域
global 监控状态 := false
global 浏览器对象
global 监控定时器
global 上次URL := ""
global 上次标题 := ""
global 上次元素数量 := 0

; 创建监控界面
CreateWebMonitorGUI() {
    global 监控窗口 := Gui("+Resize", "UIA 网页变化监控器")
    监控窗口.MarginX := 10
    监控窗口.MarginY := 10
    
    ; 标题
    监控窗口.Add("Text", "x10 y10 w500 Center", "UIA 网页变化监控器").SetFont("s14 Bold")
    
    ; 浏览器选择
    监控窗口.Add("Text", "x10 y45 w80", "浏览器:")
    browserCombo := 监控窗口.Add("ComboBox", "x100 y38 w150", ["Chrome", "Edge", "Firefox"])
    browserCombo.Text := "Chrome"
    
    ; URL输入
    监控窗口.Add("Text", "x10 y75 w80", "监控URL:")
    urlInput := 监控窗口.Add("Edit", "x100 y68 w300")
    urlInput.Text := "https://www.baidu.com"
    
    ; 控制按钮
    startBtn := 监控窗口.Add("Button", "x10 y100 w80 h30", "开始监控")
    stopBtn := 监控窗口.Add("Button", "x100 y100 w80 h30", "停止监控")
    clearBtn := 监控窗口.Add("Button", "x190 y100 w80 h30", "清空日志")
    navigateBtn := 监控窗口.Add("Button", "x280 y100 w80 h30", "导航到URL")
    testBtn := 监控窗口.Add("Button", "x370 y100 w80 h30", "测试连接")
    
    ; 状态显示
    statusLabel := 监控窗口.Add("Text", "x10 y140 w500", "状态: 未开始监控")
    statusLabel.SetFont("s10 Bold")
    
    ; 当前页面信息
    监控窗口.Add("Text", "x10 y170 w100", "当前页面信息:")
    global 页面信息区域 := 监控窗口.Add("Edit", "x10 y190 w500 h60 ReadOnly")
    
    ; 日志显示
    监控窗口.Add("Text", "x10 y260 w100", "变化日志:")
    global 日志区域 := 监控窗口.Add("Edit", "x10 y280 w500 h200 VScroll ReadOnly")
    
    ; 事件绑定
    startBtn.OnEvent("Click", StartWebMonitoring)
    stopBtn.OnEvent("Click", StopWebMonitoring)
    clearBtn.OnEvent("Click", ClearWebLog)
    navigateBtn.OnEvent("Click", NavigateToURL)
    testBtn.OnEvent("Click", TestBrowserConnection)
    
    监控窗口.OnEvent("Close", (*) => ExitApp())
    监控窗口.Show("w520 h500")

    ; 初始化页面信息显示
    页面信息区域.Text := "URL: 未连接`r`n标题: 未连接`r`n元素数量: 0`r`n按钮: 0 个`r`n链接: 0 个"
}

; 开始网页监控
StartWebMonitoring(*) {
    global 监控状态, 浏览器对象, 监控定时器
    
    if (监控状态) {
        AddWebLog("监控已在运行中...")
        return
    }
    
    try {
        ; 获取浏览器类型
        browserType := 监控窗口["ComboBox1"].Text
        browserExe := GetBrowserExe(browserType)
        
        ; 检查浏览器是否运行
        if (!WinExist("ahk_exe " . browserExe)) {
            MsgBox("请先启动 " . browserType . " 浏览器！")
            return
        }
        ; 输出所有相关窗口标题，辅助排查
        ids := WinGetList("ahk_exe " . browserExe)
        titles := ""
        for id in ids {
            title := WinGetTitle("ahk_id " id)
            titles .= id . ": " . title . "`n"
        }
        AddWebLog("检测到 " . browserType . " 窗口: " . ids.Length . " 个")
        ; MsgBox("检测到窗口:\n" . titles)  ; 注释掉调试信息
        
        ; 创建浏览器对象
        try {
            AddWebLog("正在尝试连接到 " . browserType . " 浏览器...")
            ; UIA_Browser需要窗口标识符，不是可执行文件名
            浏览器对象 := UIA_Browser("ahk_exe " . browserExe)
            if !浏览器对象 {
                throw Error("UIA_Browser 返回空对象")
            }
            AddWebLog("成功创建浏览器对象")
        } catch Error as e {
            errorMsg := "启动网页监控失败: " . e.Message
            if InStr(e.Message, "failed to find the browser") {
                errorMsg .= "`n`n可能的解决方案:"
                errorMsg .= "`n1. 确保 " . browserType . " 浏览器已经打开"
                errorMsg .= "`n2. 确保浏览器窗口可见（不是最小化状态）"
                errorMsg .= "`n3. 尝试重新启动浏览器"
                errorMsg .= "`n4. 如果使用Chrome，确保没有以无头模式运行"
            }
            MsgBox(errorMsg)
            AddWebLog("启动监控失败: " . e.Message)
            return
        }
        
        监控状态 := true
        SetTimer(CheckWebChanges, 2000)  ; 每2秒检查一次，减少干扰
        监控定时器 := CheckWebChanges
        
        ; 更新状态
        UpdateStatus("状态: 正在监控 " . browserType . " 浏览器...", "Green")
        AddWebLog("开始监控 " . browserType . " 浏览器")
        
        ; 初始化状态
        InitializeWebState()
        
    } catch Error as e {
        MsgBox("启动网页监控失败: " . e.Message)
        AddWebLog("启动监控失败: " . e.Message)
    }
}

; 停止网页监控
StopWebMonitoring(*) {
    global 监控状态, 监控定时器
    
    if (!监控状态) {
        AddWebLog("监控未在运行...")
        return
    }
    
    try {
        监控状态 := false
        if (监控定时器) {
            SetTimer(监控定时器, 0)
            监控定时器 := ""
        }
        
        UpdateStatus("状态: 监控已停止", "Red")
        AddWebLog("网页监控已停止")
        
    } catch Error as e {
        MsgBox("停止监控失败: " . e.Message)
        AddWebLog("停止监控失败: " . e.Message)
    }
}

; 初始化网页状态
InitializeWebState() {
    global 上次URL, 上次标题, 上次元素数量
    
    try {
        ; 获取当前URL和标题（使用安全模式避免激活窗口）
        上次URL := 浏览器对象.GetCurrentURL(true)
        上次标题 := WinGetTitle(浏览器对象.BrowserId)

        ; 获取页面元素数量
        try {
            elements := 浏览器对象.FindElements({})
            上次元素数量 := elements.Length
        } catch {
            上次元素数量 := 0
        }

        ; 更新页面信息显示
        UpdatePageInfo()
        AddWebLog("初始页面状态已记录")

    } catch Error as e {
        AddWebLog("初始化页面状态失败: " . e.Message)
    }
}

; 检查网页变化
CheckWebChanges() {
    global 上次URL, 上次标题, 上次元素数量
    
    if (!监控状态) {
        return
    }
    
    try {
        ; 检查浏览器是否还在运行
        browserType := 监控窗口["ComboBox1"].Text
        browserExe := GetBrowserExe(browserType)
        
        if (!WinExist("ahk_exe " . browserExe)) {
            AddWebLog("⚠️ 浏览器已关闭")
            StopWebMonitoring()
            return
        }
        
        ; 1. 检查URL变化
        CheckURLChange()
        
        ; 2. 检查标题变化
        CheckTitleChange()
        
        ; 3. 检查页面元素数量变化
        CheckElementCountChange()
        
        ; 更新页面信息
        UpdatePageInfo()
        
    } catch Error as e {
        AddWebLog("检查网页变化时出错: " . e.Message)
    }
}

; 检查URL变化
CheckURLChange() {
    global 上次URL

    try {
        ; 使用fromAddressBar=True避免激活浏览器窗口
        当前URL := 浏览器对象.GetCurrentURL(true)
        if (当前URL != 上次URL && 当前URL != "") {
            AddWebLog("🌐 URL变化: " . 当前URL)
            上次URL := 当前URL
        }
    } catch {
        ; 忽略URL检查错误
    }
}

; 检查标题变化
CheckTitleChange() {
    global 上次标题

    try {
        当前标题 := WinGetTitle(浏览器对象.BrowserId)
        if (当前标题 != 上次标题) {
            AddWebLog("📄 标题变化: " . 上次标题 . " → " . 当前标题)
            上次标题 := 当前标题
        }
    } catch {
        ; 忽略标题检查错误
    }
}

; 检查元素数量变化
CheckElementCountChange() {
    global 上次元素数量
    static 详细检查计数器 := 0

    try {
        ; 获取基本元素数量（轻量级检查）
        allElements := 浏览器对象.FindElements({})
        当前元素数量 := allElements.Length

        if (当前元素数量 != 上次元素数量) {
            AddWebLog("🔢 页面元素数量变化: " . 上次元素数量 . " → " . 当前元素数量)

            ; 只在元素数量变化较大时才进行详细检查，减少性能影响
            变化幅度 := Abs(当前元素数量 - 上次元素数量)
            if (变化幅度 > 5) {
                try {
                    buttons := 浏览器对象.FindElements({Type:"Button"})
                    links := 浏览器对象.FindElements({Type:"Hyperlink"})

                    detailInfo := "  详细信息: 按钮(" . buttons.Length . ") 链接(" . links.Length . ")"
                    AddWebLog(detailInfo)

                    ; 如果有链接变化，显示部分链接文本
                    if (links.Length > 0 && 变化幅度 > 10) {
                        linkTexts := ""
                        linkCount := Min(3, links.Length)  ; 最多显示3个链接
                        Loop linkCount {
                            try {
                                linkName := links[A_Index].Name ? links[A_Index].Name : "无标题链接"
                                linkTexts .= linkName . "; "
                            } catch {
                                continue
                            }
                        }
                        if (linkTexts != "")
                            AddWebLog("  部分链接: " . linkTexts)
                    }
                } catch {
                    ; 忽略详细信息获取错误
                }
            }

            上次元素数量 := 当前元素数量
        }
    } catch {
        ; 忽略元素数量检查错误
    }
}

; 导航到URL
NavigateToURL(*) {
    try {
        url := 监控窗口["Edit2"].Text
        if (url = "") {
            MsgBox("请输入URL！")
            return
        }
        if (!IsSet(浏览器对象) || !浏览器对象) {
            MsgBox("请先点击“开始监控”并确保监控已成功启动！")
            return
        }
        浏览器对象.Navigate(url)
        AddWebLog("导航到: " . url)
    } catch Error as e {
        MsgBox("导航失败: " . e.Message)
        AddWebLog("导航失败: " . e.Message)
    }
}

; 测试浏览器连接
TestBrowserConnection(*) {
    try {
        browserType := 监控窗口["ComboBox1"].Text
        browserExe := GetBrowserExe(browserType)
        
        if (!WinExist("ahk_exe " . browserExe)) {
            MsgBox("❌ " . browserType . " 浏览器未运行！")
            AddWebLog("测试失败: 浏览器未运行")
            return
        }
        
        ; 尝试创建浏览器对象
        testBrowser := UIA_Browser("ahk_exe " . browserExe)
        currentURL := testBrowser.GetCurrentURL()
        currentTitle := WinGetTitle(testBrowser.BrowserId)

        MsgBox("✅ 连接成功！`n浏览器: " . browserType . "`nURL: " . currentURL . "`n标题: " . currentTitle)
        AddWebLog("测试成功: 已连接到 " . browserType)

        ; 临时更新页面信息显示以测试连接
        tempInfo := "URL: " . currentURL . "`r`n"
        tempInfo .= "标题: " . currentTitle . "`r`n"
        tempInfo .= "状态: 测试连接成功"
        页面信息区域.Text := tempInfo
        
    } catch Error as e {
        MsgBox("❌ 连接失败: " . e.Message)
        AddWebLog("测试失败: " . e.Message)
    }
}

; 获取浏览器可执行文件名
GetBrowserExe(browserType) {
    switch browserType {
        case "Chrome": return "chrome.exe"
        case "Edge": return "msedge.exe"
        case "Firefox": return "firefox.exe"
        default: return "chrome.exe"
    }
}

; 更新状态显示
UpdateStatus(text, color := "Black") {
    for control in 监控窗口 {
        if (InStr(control.Text, "状态:")) {
            control.Text := text
            control.SetFont("s10 Bold", color)
            break
        }
    }
}

; 更新页面信息显示
UpdatePageInfo() {
    try {
        if (!IsSet(浏览器对象) || !浏览器对象) {
            页面信息区域.Text := "URL: 未连接`r`n标题: 未连接`r`n元素数量: 0`r`n按钮: 0 个`r`n链接: 0 个"
            return
        }

        ; 使用安全模式获取URL，避免激活浏览器窗口
        currentURL := ""
        currentTitle := ""

        try {
            currentURL := 浏览器对象.GetCurrentURL(true)
            if (!currentURL || currentURL = "")
                currentURL := "获取失败"
        } catch {
            currentURL := "获取失败"
        }

        try {
            currentTitle := WinGetTitle(浏览器对象.BrowserId)
            if (!currentTitle || currentTitle = "")
                currentTitle := "获取失败"
        } catch {
            currentTitle := "获取失败"
        }

        info := "URL: " . currentURL . "`r`n"
        info .= "标题: " . currentTitle . "`r`n"
        info .= "元素数量: " . 上次元素数量

        ; 添加更多详细信息
        try {
            buttons := 浏览器对象.FindElements({Type:"Button"})
            links := 浏览器对象.FindElements({Type:"Hyperlink"})
            info .= "`r`n按钮: " . buttons.Length . " 个"
            info .= "`r`n链接: " . links.Length . " 个"
        } catch {
            info .= "`r`n按钮: 获取失败"
            info .= "`r`n链接: 获取失败"
        }

        ; 直接更新页面信息区域
        页面信息区域.Text := info
    } catch Error as e {
        ; 显示错误信息
        页面信息区域.Text := "更新页面信息时出错:`r`n" . e.Message
    }
}

; 清空日志
ClearWebLog(*) {
    日志区域.Text := ""
    AddWebLog("日志已清空")
}

; 添加日志
AddWebLog(message) {
    timestamp := FormatTime(A_Now, "HH:mm:ss")
    newLog := "[" . timestamp . "] " . message . "`r`n"
    日志区域.Text .= newLog

    ; 自动滚动到底部（不影响其他窗口焦点）
    ; 保存当前活动窗口
    currentActiveWindow := WinExist("A")

    ; 只有当监控窗口是活动窗口时才滚动日志
    if (currentActiveWindow = 监控窗口.Hwnd) {
        日志区域.Focus()
        Send("^{End}")
    } else {
        ; 如果监控窗口不是活动窗口，使用更安全的方式滚动
        ; 通过设置选择范围来滚动到底部，不会影响焦点
        textLength := StrLen(日志区域.Text)
        日志区域.SetSelection(textLength, textLength)
    }
}

; 启动程序
CreateWebMonitorGUI()
