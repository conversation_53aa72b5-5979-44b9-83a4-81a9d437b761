﻿=== UIA控件扫描报告 ===
扫描时间: 2025-08-01 15:53:49
目标窗口: test00.ahk - project - Visual Studio Code [管理员] [UIA]
控件总数: 1367
===================================================

控件 #1
路径: Descendant_1: 50030[test00.ahk - project - Visual Studio Code [管理员]]{746064}
类型: 50030
名称: test00.ahk - project - Visual Studio Code [管理员]
值: vscode-file://vscode-app/d:/Program%20Files/Microsoft%20VS%20Code/resources/app/out/vs/code/electron-browser/workbench/workbench.html
自动化ID: 746064
---------------------------------------------------
控件 #2
路径: Descendant_2: 50032
类型: 50032
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #3
路径: Descendant_3: 50010
类型: 50010
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #4
路径: Descendant_4: 50011[文件]
类型: 50011
名称: 文件
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #5
路径: Descendant_5: 50011[编辑]
类型: 50011
名称: 编辑
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #6
路径: Descendant_6: 50011[选择]
类型: 50011
名称: 选择
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #7
路径: Descendant_7: 50011[查看]
类型: 50011
名称: 查看
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #8
路径: Descendant_8: 50011[转到]
类型: 50011
名称: 转到
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #9
路径: Descendant_9: 50011[运行]
类型: 50011
名称: 运行
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #10
路径: Descendant_10: 50011[终端]
类型: 50011
名称: 终端
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #11
路径: Descendant_11: 50011[帮助]
类型: 50011
名称: 帮助
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #12
路径: Descendant_12: 50021
类型: 50021
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #13
路径: Descendant_13: 50000[返回 (Alt+LeftArrow)]
类型: 50000
名称: 返回 (Alt+LeftArrow)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #14
路径: Descendant_14: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #15
路径: Descendant_15: 50000[前进 (Alt+RightArrow)]
类型: 50000
名称: 前进 (Alt+RightArrow)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #16
路径: Descendant_16: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #17
路径: Descendant_17: 50021
类型: 50021
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #18
路径: Descendant_18: 50000[project [管理员]]
类型: 50000
名称: project [管理员]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #19
路径: Descendant_19: 50000[已达到 Copilot Free 计划聊天消息配额。单击以获取详细信息。]
类型: 50000
名称: 已达到 Copilot Free 计划聊天消息配额。单击以获取详细信息。
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #20
路径: Descendant_20: 50000[已达到 Copilot Free 计划聊天消息配额。单击以获取详细信息。]
类型: 50000
名称: 已达到 Copilot Free 计划聊天消息配额。单击以获取详细信息。
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #21
路径: Descendant_21: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #22
路径: Descendant_22: 50005[更多...]
类型: 50005
名称: 更多...
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #23
路径: Descendant_23: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #24
路径: Descendant_24: 50021[标题操作]
类型: 50021
名称: 标题操作
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #25
路径: Descendant_25: 50000[自定义布局...]
类型: 50000
名称: 自定义布局...
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #26
路径: Descendant_26: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #27
路径: Descendant_27: 50002[切换主侧栏 (Ctrl+B)]
类型: 50002
名称: 切换主侧栏 (Ctrl+B)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #28
路径: Descendant_28: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #29
路径: Descendant_29: 50002[切换面板 (Ctrl+J)]
类型: 50002
名称: 切换面板 (Ctrl+J)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #30
路径: Descendant_30: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #31
路径: Descendant_31: 50002[切换辅助侧栏 (Ctrl+Alt+B)]
类型: 50002
名称: 切换辅助侧栏 (Ctrl+Alt+B)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #32
路径: Descendant_32: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #33
路径: Descendant_33: 50018[活动视图切换器]
类型: 50018
名称: 活动视图切换器
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #34
路径: Descendant_34: 50019[资源管理器 (Ctrl+Shift+E)]
类型: 50019
名称: 资源管理器 (Ctrl+Shift+E)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #35
路径: Descendant_35: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #36
路径: Descendant_36: 50019[搜索 (Ctrl+Shift+F)]
类型: 50019
名称: 搜索 (Ctrl+Shift+F)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #37
路径: Descendant_37: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #38
路径: Descendant_38: 50019[源代码管理 (Ctrl+Shift+G)]
类型: 50019
名称: 源代码管理 (Ctrl+Shift+G)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #39
路径: Descendant_39: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #40
路径: Descendant_40: 50019[运行和调试 (Ctrl+Shift+D)]
类型: 50019
名称: 运行和调试 (Ctrl+Shift+D)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #41
路径: Descendant_41: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #42
路径: Descendant_42: 50019[扩展 (Ctrl+Shift+X)]
类型: 50019
名称: 扩展 (Ctrl+Shift+X)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #43
路径: Descendant_43: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #44
路径: Descendant_44: 50019[Augment]
类型: 50019
名称: Augment
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #45
路径: Descendant_45: 50021[管理]
类型: 50021
名称: 管理
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #46
路径: Descendant_46: 50011[帐户]
类型: 50011
名称: 帐户
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #47
路径: Descendant_47: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #48
路径: Descendant_48: 50011[管理]
类型: 50011
名称: 管理
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #49
路径: Descendant_49: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #50
路径: Descendant_50: 50021[Augment操作]
类型: 50021
名称: Augment操作
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #51
路径: Descendant_51: 50005[Augment Options]
类型: 50005
名称: Augment Options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #52
路径: Descendant_52: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #53
路径: Descendant_53: 50021
类型: 50021
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #54
路径: Descendant_54: 50026{workbench.parts.editor}
类型: 50026
名称: [无名称]
值: [无值]
自动化ID: workbench.parts.editor
---------------------------------------------------
控件 #55
路径: Descendant_55: 50018
类型: 50018
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #56
路径: Descendant_56: 50019[test00.ahk]
类型: 50019
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #57
路径: Descendant_57: 50020[  ]
类型: 50020
名称:   
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #58
路径: Descendant_58: 50020[test00.ahk]
类型: 50020
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #59
路径: Descendant_59: 50021[选项卡操作]
类型: 50021
名称: 选项卡操作
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #60
路径: Descendant_60: 50000[关闭 (Ctrl+F4)]
类型: 50000
名称: 关闭 (Ctrl+F4)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #61
路径: Descendant_61: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #62
路径: Descendant_62: 50021[编辑器操作]
类型: 50021
名称: 编辑器操作
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #63
路径: Descendant_63: 50026
类型: 50026
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #64
路径: Descendant_64: 50000[停止运行中的脚本 (Ctrl+F6)]
类型: 50000
名称: 停止运行中的脚本 (Ctrl+F6)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #65
路径: Descendant_65: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #66
路径: Descendant_66: 50005[运行或调试...]
类型: 50005
名称: 运行或调试...
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #67
路径: Descendant_67: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #68
路径: Descendant_68: 50005[Next Edit]
类型: 50005
名称: Next Edit
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #69
路径: Descendant_69: 50000[向右拆分编辑器 (Ctrl+\) [Alt] 向下拆分编辑器]
类型: 50000
名称: 向右拆分编辑器 (Ctrl+\) [Alt] 向下拆分编辑器
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #70
路径: Descendant_70: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #71
路径: Descendant_71: 50005[更多操作...]
类型: 50005
名称: 更多操作...
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #72
路径: Descendant_72: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #73
路径: Descendant_73: 50008
类型: 50008
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #74
路径: Descendant_74: 50007
类型: 50007
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #75
路径: Descendant_75: 50020[  ]
类型: 50020
名称:   
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #76
路径: Descendant_76: 50020[test00.ahk]
类型: 50020
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #77
路径: Descendant_77: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #78
路径: Descendant_78: 50007
类型: 50007
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #79
路径: Descendant_79: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #80
路径: Descendant_80: 50020[RefreshWindowList]
类型: 50020
名称: RefreshWindowList
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #81
路径: Descendant_81: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #82
路径: Descendant_82: 50007
类型: 50007
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #83
路径: Descendant_83: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #84
路径: Descendant_84: 50020[processName]
类型: 50020
名称: processName
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #85
路径: Descendant_85: 50004[现在无法访问编辑器。 若要启用屏幕阅读器优化模式，请使用 Shift+Alt+F1]
类型: 50004
名称: 现在无法访问编辑器。 若要启用屏幕阅读器优化模式，请使用 Shift+Alt+F1
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #86
路径: Descendant_86: 50004
类型: 50004
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #87
路径: Descendant_87: 50020[64]
类型: 50020
名称: 64
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #88
路径: Descendant_88: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #89
路径: Descendant_89: 50008
类型: 50008
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #90
路径: Descendant_90: 50007
类型: 50007
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #91
路径: Descendant_91: 50020[RefreshWindowList]
类型: 50020
名称: RefreshWindowList
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #92
路径: Descendant_92: 50020[(]
类型: 50020
名称: (
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #93
路径: Descendant_93: 50020[*]
类型: 50020
名称: *
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #94
路径: Descendant_94: 50020[)]
类型: 50020
名称: )
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #95
路径: Descendant_95: 50020[ ]
类型: 50020
名称:  
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #96
路径: Descendant_96: 50020[{]
类型: 50020
名称: {
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #97
路径: Descendant_97: 50018[活动视图切换器]
类型: 50018
名称: 活动视图切换器
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #98
路径: Descendant_98: 50019[问题 (Ctrl+Shift+M)]
类型: 50019
名称: 问题 (Ctrl+Shift+M)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #99
路径: Descendant_99: 50020[问题]
类型: 50020
名称: 问题
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #100
路径: Descendant_100: 50019[输出 (Ctrl+Shift+U)]
类型: 50019
名称: 输出 (Ctrl+Shift+U)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #101
路径: Descendant_101: 50020[输出]
类型: 50020
名称: 输出
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #102
路径: Descendant_102: 50019[调试控制台 (Ctrl+Shift+Y)]
类型: 50019
名称: 调试控制台 (Ctrl+Shift+Y)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #103
路径: Descendant_103: 50020[调试控制台]
类型: 50020
名称: 调试控制台
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #104
路径: Descendant_104: 50019[终端 (Ctrl+`)]
类型: 50019
名称: 终端 (Ctrl+`)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #105
路径: Descendant_105: 50020[终端]
类型: 50020
名称: 终端
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #106
路径: Descendant_106: 50019[端口]
类型: 50019
名称: 端口
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #107
路径: Descendant_107: 50019[Augment Next Edit]
类型: 50019
名称: Augment Next Edit
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #108
路径: Descendant_108: 50020[AUGMENT NEXT EDIT]
类型: 50020
名称: AUGMENT NEXT EDIT
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #109
路径: Descendant_109: 50021[输出操作]
类型: 50021
名称: 输出操作
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #110
路径: Descendant_110: 50004[筛选器]
类型: 50004
名称: 筛选器
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #111
路径: Descendant_111: 50021
类型: 50021
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #112
路径: Descendant_112: 50003
类型: 50003
名称: [无名称]
值: AutoHotkey2
自动化ID: [无ID]
---------------------------------------------------
控件 #113
路径: Descendant_113: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #114
路径: Descendant_114: 50000[清除输出]
类型: 50000
名称: 清除输出
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #115
路径: Descendant_115: 50002[关闭自动滚动]
类型: 50002
名称: 关闭自动滚动
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #116
路径: Descendant_116: 50005[视图和更多操作…]
类型: 50005
名称: 视图和更多操作…
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #117
路径: Descendant_117: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #118
路径: Descendant_118: 50021
类型: 50021
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #119
路径: Descendant_119: 50002[最大化面板大小]
类型: 50002
名称: 最大化面板大小
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #120
路径: Descendant_120: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #121
路径: Descendant_121: 50000[隐藏面板 (Ctrl+J)]
类型: 50000
名称: 隐藏面板 (Ctrl+J)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #122
路径: Descendant_122: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #123
路径: Descendant_123: 50030[AutoHotkey2 - 输出]
类型: 50030
名称: AutoHotkey2 - 输出
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #124
路径: Descendant_124: 50004[现在无法访问编辑器。 若要启用屏幕阅读器优化模式，请使用 Shift+Alt+F1]
类型: 50004
名称: 现在无法访问编辑器。 若要启用屏幕阅读器优化模式，请使用 Shift+Alt+F1
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #125
路径: Descendant_125: 50004
类型: 50004
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #126
路径: Descendant_126: 50017{workbench.parts.statusbar}
类型: 50017
名称: [无名称]
值: [无值]
自动化ID: workbench.parts.statusbar
---------------------------------------------------
控件 #127
路径: Descendant_127: 50026[remote]{status.host}
类型: 50026
名称: remote
值: [无值]
自动化ID: status.host
---------------------------------------------------
控件 #128
路径: Descendant_128: 50000[remote]
类型: 50000
名称: remote
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #129
路径: Descendant_129: 50026[AutoHotkey 2.0.19 64 bit, C:\AutoHotkey\v2\AutoHotkey.exe]{thqby.vscode-autohotkey2-lsp}
类型: 50026
名称: AutoHotkey 2.0.19 64 bit, C:\AutoHotkey\v2\AutoHotkey.exe
值: [无值]
自动化ID: thqby.vscode-autohotkey2-lsp
---------------------------------------------------
控件 #130
路径: Descendant_130: 50000[AutoHotkey 2.0.19 64 bit, C:\AutoHotkey\v2\AutoHotkey.exe]
类型: 50000
名称: AutoHotkey 2.0.19 64 bit, C:\AutoHotkey\v2\AutoHotkey.exe
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #131
路径: Descendant_131: 50026[没有问题]{status.problems}
类型: 50026
名称: 没有问题
值: [无值]
自动化ID: status.problems
---------------------------------------------------
控件 #132
路径: Descendant_132: 50000[没有问题]
类型: 50000
名称: 没有问题
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #133
路径: Descendant_133: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #134
路径: Descendant_134: 50020
类型: 50020
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #135
路径: Descendant_135: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #136
路径: Descendant_136: 50020
类型: 50020
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #137
路径: Descendant_137: 50026[通知]{status.notifications}
类型: 50026
名称: 通知
值: [无值]
自动化ID: status.notifications
---------------------------------------------------
控件 #138
路径: Descendant_138: 50000[通知]
类型: 50000
名称: 通知
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #139
路径: Descendant_139: 50026[augment-icon-smile Augment, Open Augment]{Augment.vscode-augment.vscode-augment.PrimaryStatusBarItem}
类型: 50026
名称: augment-icon-smile Augment, Open Augment
值: [无值]
自动化ID: Augment.vscode-augment.vscode-augment.PrimaryStatusBarItem
---------------------------------------------------
控件 #140
路径: Descendant_140: 50000[augment-icon-smile Augment, Open Augment]
类型: 50000
名称: augment-icon-smile Augment, Open Augment
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #141
路径: Descendant_141: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #142
路径: Descendant_142: 50020[ Augment]
类型: 50020
名称:  Augment
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #143
路径: Descendant_143: 50026[已达到聊天配额]{chat.statusBarEntry}
类型: 50026
名称: 已达到聊天配额
值: [无值]
自动化ID: chat.statusBarEntry
---------------------------------------------------
控件 #144
路径: Descendant_144: 50000[已达到聊天配额]
类型: 50000
名称: 已达到聊天配额
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #145
路径: Descendant_145: 50020[]
类型: 50020
名称: 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #146
路径: Descendant_146: 50020[ 已达到聊天配额]
类型: 50020
名称:  已达到聊天配额
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #147
路径: Descendant_147: 50026[AutoHotkey2]{status.editor.mode}
类型: 50026
名称: AutoHotkey2
值: [无值]
自动化ID: status.editor.mode
---------------------------------------------------
控件 #148
路径: Descendant_148: 50000[AutoHotkey2]
类型: 50000
名称: AutoHotkey2
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #149
路径: Descendant_149: 50026[编辑器语言状态: $(folder)syntaxes, next: Copilot Completions, next: $(copilot) 无内联建议可用，内联建议]{status.languageStatus}
类型: 50026
名称: 编辑器语言状态: $(folder)syntaxes, next: Copilot Completions, next: $(copilot) 无内联建议可用，内联建议
值: [无值]
自动化ID: status.languageStatus
---------------------------------------------------
控件 #150
路径: Descendant_150: 50000[编辑器语言状态: $(folder)syntaxes, next: Copilot Completions, next: $(copilot) 无内联建议可用，内联建议]
类型: 50000
名称: 编辑器语言状态: $(folder)syntaxes, next: Copilot Completions, next: $(copilot) 无内联建议可用，内联建议
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #151
路径: Descendant_151: 50026[CRLF]{status.editor.eol}
类型: 50026
名称: CRLF
值: [无值]
自动化ID: status.editor.eol
---------------------------------------------------
控件 #152
路径: Descendant_152: 50000[CRLF]
类型: 50000
名称: CRLF
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #153
路径: Descendant_153: 50026[UTF-8]{status.editor.encoding}
类型: 50026
名称: UTF-8
值: [无值]
自动化ID: status.editor.encoding
---------------------------------------------------
控件 #154
路径: Descendant_154: 50000[UTF-8]
类型: 50000
名称: UTF-8
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #155
路径: Descendant_155: 50026[空格: 4]{status.editor.indentation}
类型: 50026
名称: 空格: 4
值: [无值]
自动化ID: status.editor.indentation
---------------------------------------------------
控件 #156
路径: Descendant_156: 50000[空格: 4]
类型: 50000
名称: 空格: 4
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #157
路径: Descendant_157: 50026[行 81，列 55]{status.editor.selection}
类型: 50026
名称: 行 81，列 55
值: [无值]
自动化ID: status.editor.selection
---------------------------------------------------
控件 #158
路径: Descendant_158: 50000[行 81，列 55]
类型: 50000
名称: 行 81，列 55
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #159
路径: Descendant_159: 50025
类型: 50025
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #160
路径: Descendant_160: 50020[错误: Unable to find a Git executable. Either: Set the Visual Studio Code Setting "git.path" to the path and filename of an existing Git executable, or install Git and restart Visual Studio Code.]
类型: 50020
名称: 错误: Unable to find a Git executable. Either: Set the Visual Studio Code Setting "git.path" to the path and filename of an existing Git executable, or install Git and restart Visual Studio Code.
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #161
路径: Descendant_161: 50025
类型: 50025
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #162
路径: Descendant_162: 50026
类型: 50026
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #163
路径: Descendant_163: 50026
类型: 50026
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #164
路径: Descendant_164: 50033
类型: 50033
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #165
路径: Descendant_165: 50030
类型: 50030
名称: [无名称]
值: vscode-webview://1q1psribpjh54p1slku6coqip78gskstapcgm01c0lr3vghaq5uu/index.html?id=a1685643-ee73-4b7a-83a2-8b8fc4f6ab13&parentId=1&origin=bca72959-5e2e-46a5-8d93-860673484d3f&swVersion=4&extensionId=Augment.vscode-augment&platform=electron&vscode-resource-base-authority=vscode-resource.vscode-cdn.net&parentOrigin=vscode-file%3A%2F%2Fvscode-app&purpose=webviewView
自动化ID: [无ID]
---------------------------------------------------
控件 #166
路径: Descendant_166: 50030
类型: 50030
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #167
路径: Descendant_167: 50033[Augment]{active-frame}
类型: 50033
名称: Augment
值: [无值]
自动化ID: active-frame
---------------------------------------------------
控件 #168
路径: Descendant_168: 50030[Augment]
类型: 50030
名称: Augment
值: vscode-webview://1q1psribpjh54p1slku6coqip78gskstapcgm01c0lr3vghaq5uu/index.html?id=a1685643-ee73-4b7a-83a2-8b8fc4f6ab13&parentId=1&origin=bca72959-5e2e-46a5-8d93-860673484d3f&swVersion=4&extensionId=Augment.vscode-augment&platform=electron&vscode-resource-base-authority=vscode-resource.vscode-cdn.net&parentOrigin=vscode-file%3A%2F%2Fvscode-app&purpose=webviewView
自动化ID: [无ID]
---------------------------------------------------
控件 #169
路径: Descendant_169: 50030
类型: 50030
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #170
路径: Descendant_170: 50026
类型: 50026
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #171
路径: Descendant_171: 50026[Remote agents list]
类型: 50026
名称: Remote agents list
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #172
路径: Descendant_172: 50000[Expand threads]
类型: 50000
名称: Expand threads
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #173
路径: Descendant_173: 50006
类型: 50006
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #174
路径: Descendant_174: 50020[Threads]
类型: 50020
名称: Threads
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #175
路径: Descendant_175: 50000[Agent]
类型: 50000
名称: Agent
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #176
路径: Descendant_176: 50000[Agent]
类型: 50000
名称: Agent
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #177
路径: Descendant_177: 50006
类型: 50006
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #178
路径: Descendant_178: 50020[Agent]
类型: 50020
名称: Agent
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #179
路径: Descendant_179: 50006
类型: 50006
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #180
路径: Descendant_180: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #181
路径: Descendant_181: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #182
路径: Descendant_182: 50006
类型: 50006
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #183
路径: Descendant_183: 50000[Edit Thread Title]
类型: 50000
名称: Edit Thread Title
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #184
路径: Descendant_184: 50006
类型: 50006
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #185
路径: Descendant_185: 50000[Fixed AutoHotkey Range function error ]
类型: 50000
名称: Fixed AutoHotkey Range function error 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #186
路径: Descendant_186: 50000[Fixed AutoHotkey Range function error ]
类型: 50000
名称: Fixed AutoHotkey Range function error 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #187
路径: Descendant_187: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #188
路径: Descendant_188: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #189
路径: Descendant_189: 50006
类型: 50006
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #190
路径: Descendant_190: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #191
路径: Descendant_191: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #192
路径: Descendant_192: 50006
类型: 50006
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #193
路径: Descendant_193: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #194
路径: Descendant_194: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #195
路径: Descendant_195: 50006
类型: 50006
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #196
路径: Descendant_196: 50026
类型: 50026
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #197
路径: Descendant_197: 50000[18:05]
类型: 50000
名称: 18:05
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #198
路径: Descendant_198: 50020[18:05]
类型: 50020
名称: 18:05
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #199
路径: Descendant_199: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #200
路径: Descendant_200: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #201
路径: Child: 50030[test00.ahk - project - Visual Studio Code [管理员]]{746064}
类型: 50030
名称: test00.ahk - project - Visual Studio Code [管理员]
值: vscode-file://vscode-app/d:/Program%20Files/Microsoft%20VS%20Code/resources/app/out/vs/code/electron-browser/workbench/workbench.html
自动化ID: 746064
---------------------------------------------------
控件 #202
路径: GrandChild: 50032
类型: 50032
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #203
路径: Child: 50033
类型: 50033
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #204
路径: Child: 50037{TitleBar}
类型: 50037
名称: [无名称]
值: test00.ahk - project - Visual Studio Code [管理员]
自动化ID: TitleBar
---------------------------------------------------
控件 #205
路径: Child: 50033
类型: 50033
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #206
路径: GrandChild: 50033
类型: 50033
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #207
路径: Button: 50000[返回 (Alt+LeftArrow)]
类型: 50000
名称: 返回 (Alt+LeftArrow)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #208
路径: Button: 50000[前进 (Alt+RightArrow)]
类型: 50000
名称: 前进 (Alt+RightArrow)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #209
路径: Button: 50000[project [管理员]]
类型: 50000
名称: project [管理员]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #210
路径: Button: 50000[已达到 Copilot Free 计划聊天消息配额。单击以获取详细信息。]
类型: 50000
名称: 已达到 Copilot Free 计划聊天消息配额。单击以获取详细信息。
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #211
路径: Button: 50000[已达到 Copilot Free 计划聊天消息配额。单击以获取详细信息。]
类型: 50000
名称: 已达到 Copilot Free 计划聊天消息配额。单击以获取详细信息。
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #212
路径: Button: 50000[自定义布局...]
类型: 50000
名称: 自定义布局...
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #213
路径: Button: 50000[关闭 (Ctrl+F4)]
类型: 50000
名称: 关闭 (Ctrl+F4)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #214
路径: Button: 50000[停止运行中的脚本 (Ctrl+F6)]
类型: 50000
名称: 停止运行中的脚本 (Ctrl+F6)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #215
路径: Button: 50000[向右拆分编辑器 (Ctrl+\) [Alt] 向下拆分编辑器]
类型: 50000
名称: 向右拆分编辑器 (Ctrl+\) [Alt] 向下拆分编辑器
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #216
路径: Button: 50000[清除输出]
类型: 50000
名称: 清除输出
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #217
路径: Button: 50000[隐藏面板 (Ctrl+J)]
类型: 50000
名称: 隐藏面板 (Ctrl+J)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #218
路径: Button: 50000[remote]
类型: 50000
名称: remote
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #219
路径: Button: 50000[AutoHotkey 2.0.19 64 bit, C:\AutoHotkey\v2\AutoHotkey.exe]
类型: 50000
名称: AutoHotkey 2.0.19 64 bit, C:\AutoHotkey\v2\AutoHotkey.exe
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #220
路径: Button: 50000[没有问题]
类型: 50000
名称: 没有问题
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #221
路径: Button: 50000[通知]
类型: 50000
名称: 通知
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #222
路径: Button: 50000[augment-icon-smile Augment, Open Augment]
类型: 50000
名称: augment-icon-smile Augment, Open Augment
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #223
路径: Button: 50000[已达到聊天配额]
类型: 50000
名称: 已达到聊天配额
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #224
路径: Button: 50000[AutoHotkey2]
类型: 50000
名称: AutoHotkey2
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #225
路径: Button: 50000[编辑器语言状态: $(folder)syntaxes, next: Copilot Completions, next: $(copilot) 无内联建议可用，内联建议]
类型: 50000
名称: 编辑器语言状态: $(folder)syntaxes, next: Copilot Completions, next: $(copilot) 无内联建议可用，内联建议
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #226
路径: Button: 50000[CRLF]
类型: 50000
名称: CRLF
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #227
路径: Button: 50000[UTF-8]
类型: 50000
名称: UTF-8
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #228
路径: Button: 50000[空格: 4]
类型: 50000
名称: 空格: 4
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #229
路径: Button: 50000[行 81，列 55]
类型: 50000
名称: 行 81，列 55
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #230
路径: Button: 50000[Expand threads]
类型: 50000
名称: Expand threads
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #231
路径: Button: 50000[Agent]
类型: 50000
名称: Agent
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #232
路径: Button: 50000[Agent]
类型: 50000
名称: Agent
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #233
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #234
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #235
路径: Button: 50000[Edit Thread Title]
类型: 50000
名称: Edit Thread Title
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #236
路径: Button: 50000[Fixed AutoHotkey Range function error ]
类型: 50000
名称: Fixed AutoHotkey Range function error 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #237
路径: Button: 50000[Fixed AutoHotkey Range function error ]
类型: 50000
名称: Fixed AutoHotkey Range function error 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #238
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #239
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #240
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #241
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #242
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #243
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #244
路径: Button: 50000[18:05]
类型: 50000
名称: 18:05
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #245
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #246
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #247
路径: Button: 50000[Fix the selected code. The IDE reports the following issue: • L351: 变量'Range'似乎从未被赋值]
类型: 50000
名称: Fix the selected code. The IDE reports the following issue: • L351: 变量'Range'似乎从未被赋值
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #248
路径: Button: 50000[Fix the selected code. The IDE reports the following issue: • L351: 变量'Range'似乎从未被赋值]
类型: 50000
名称: Fix the selected code. The IDE reports the following issue: • L351: 变量'Range'似乎从未被赋值
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #249
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #250
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #251
路径: Button: 50000[Read lines 345-360 test00.ahk]
类型: 50000
名称: Read lines 345-360 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #252
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #253
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #254
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #255
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #256
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #257
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #258
路径: Button: 50000[Pattern Search Range\( test00.ahk]
类型: 50000
名称: Pattern Search Range\( test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #259
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #260
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #261
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #262
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #263
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #264
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #265
路径: Button: 50000[Pattern Search for.*in test00.ahk]
类型: 50000
名称: Pattern Search for.*in test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #266
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #267
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #268
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #269
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #270
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #271
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #272
路径: Button: 50000[Pattern Search Loop [0-9] test00.ahk]
类型: 50000
名称: Pattern Search Loop [0-9] test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #273
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #274
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #275
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #276
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #277
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #278
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #279
路径: Button: 50000[Pattern Search Loop test00.ahk]
类型: 50000
名称: Pattern Search Loop test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #280
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #281
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #282
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #283
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #284
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #285
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #286
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #287
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #288
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #289
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #290
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #291
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #292
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #293
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #294
路径: Button: 50000[ test00.ahk]
类型: 50000
名称:  test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #295
路径: Button: 50000[ test00.ahk]
类型: 50000
名称:  test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #296
路径: Button: 50000[ test00.ahk]
类型: 50000
名称:  test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #297
路径: Button: 50000[Copy]
类型: 50000
名称: Copy
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #298
路径: Button: 50000[Copy]
类型: 50000
名称: Copy
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #299
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #300
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #301
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #302
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #303
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #304
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #305
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #306
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #307
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #308
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #309
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #310
路径: Button: 50000[Checkpoint 1]
类型: 50000
名称: Checkpoint 1
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #311
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #312
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #313
路径: Button: 50000[18:10]
类型: 50000
名称: 18:10
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #314
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #315
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #316
路径: Button: 50000[c 监控窗口怎么为空，怎么选择监控窗口的程序？]
类型: 50000
名称: c 监控窗口怎么为空，怎么选择监控窗口的程序？
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #317
路径: Button: 50000[c 监控窗口怎么为空，怎么选择监控窗口的程序？]
类型: 50000
名称: c 监控窗口怎么为空，怎么选择监控窗口的程序？
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #318
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #319
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #320
路径: Button: 50000[Pattern Search 监控窗口|MonitorWindow|窗口监控 test00.ahk]
类型: 50000
名称: Pattern Search 监控窗口|MonitorWindow|窗口监控 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #321
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #322
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #323
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #324
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #325
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #326
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #327
路径: Button: 50000[Pattern Search WinGet|WinActive|窗口|window test00.ahk]
类型: 50000
名称: Pattern Search WinGet|WinActive|窗口|window test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #328
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #329
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #330
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #331
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #332
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #333
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #334
路径: Button: 50000[Pattern Search StartMouseMonitor|监控|Monitor test00.ahk]
类型: 50000
名称: Pattern Search StartMouseMonitor|监控|Monitor test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #335
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #336
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #337
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #338
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #339
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #340
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #341
路径: Button: 50000[Read lines 126-150 test00.ahk]
类型: 50000
名称: Read lines 126-150 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #342
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #343
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #344
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #345
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #346
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #347
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #348
路径: Button: 50000[Read lines 42-62 test00.ahk]
类型: 50000
名称: Read lines 42-62 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #349
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #350
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #351
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #352
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #353
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #354
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #355
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #356
路径: Button: 50000[ test00.ahk]
类型: 50000
名称:  test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #357
路径: Button: 50000[ test00.ahk]
类型: 50000
名称:  test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #358
路径: Button: 50000[ test00.ahk]
类型: 50000
名称:  test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #359
路径: Button: 50000[Copy]
类型: 50000
名称: Copy
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #360
路径: Button: 50000[Copy]
类型: 50000
名称: Copy
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #361
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #362
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #363
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #364
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #365
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #366
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #367
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #368
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #369
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #370
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #371
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #372
路径: Button: 50000[18:13]
类型: 50000
名称: 18:13
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #373
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #374
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #375
路径: Button: 50000[点击"刷新窗口"按钮没有反应，什么都没有监控到，是什么问题？]
类型: 50000
名称: 点击"刷新窗口"按钮没有反应，什么都没有监控到，是什么问题？
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #376
路径: Button: 50000[点击"刷新窗口"按钮没有反应，什么都没有监控到，是什么问题？]
类型: 50000
名称: 点击"刷新窗口"按钮没有反应，什么都没有监控到，是什么问题？
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #377
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #378
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #379
路径: Button: 50000[Read lines 1-50 test00.ahk]
类型: 50000
名称: Read lines 1-50 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #380
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #381
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #382
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #383
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #384
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #385
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #386
路径: Button: 50000[Read lines 42-70 test00.ahk]
类型: 50000
名称: Read lines 42-70 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #387
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #388
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #389
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #390
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #391
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #392
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #393
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #394
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #395
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #396
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #397
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #398
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #399
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #400
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #401
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #402
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #403
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #404
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #405
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #406
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #407
路径: Button: 50000[Terminal $ .\test00.ahk]
类型: 50000
名称: Terminal $ .\test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #408
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #409
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #410
路径: Button: 50000[Terminal Read from Process]
类型: 50000
名称: Terminal Read from Process
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #411
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #412
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #413
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #414
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #415
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #416
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #417
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #418
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #419
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #420
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #421
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #422
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #423
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #424
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #425
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #426
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #427
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #428
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #429
路径: Button: 50000[Checkpoint 2]
类型: 50000
名称: Checkpoint 2
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #430
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #431
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #432
路径: Button: 50000[18:15]
类型: 50000
名称: 18:15
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #433
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #434
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #435
路径: Button: 50000[Error: This value of type "Gui.ListBox" has no method named "GetCount". 084: } 085: } ▶ 088: MsgBox("扫描完成！ 总窗口数: " . windowCount . " 可用窗口数: " . addedCount . " 列表项数: " . listBox.GetCount())报错了]
类型: 50000
名称: Error: This value of type "Gui.ListBox" has no method named "GetCount". 084: } 085: } ▶ 088: MsgBox("扫描完成！ 总窗口数: " . windowCount . " 可用窗口数: " . addedCount . " 列表项数: " . listBox.GetCount())报错了
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #436
路径: Button: 50000[Error: This value of type "Gui.ListBox" has no method named "GetCount". 084: } 085: } ▶ 088: MsgBox("扫描完成！ 总窗口数: " . windowCount . " 可用窗口数: " . addedCount . " 列表项数: " . listBox.GetCount())报错了]
类型: 50000
名称: Error: This value of type "Gui.ListBox" has no method named "GetCount". 084: } 085: } ▶ 088: MsgBox("扫描完成！ 总窗口数: " . windowCount . " 可用窗口数: " . addedCount . " 列表项数: " . listBox.GetCount())报错了
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #437
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #438
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #439
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #440
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #441
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #442
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #443
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #444
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #445
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #446
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #447
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #448
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #449
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #450
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #451
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #452
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #453
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #454
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #455
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #456
路径: Button: 50000[Checkpoint 3]
类型: 50000
名称: Checkpoint 3
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #457
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #458
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #459
路径: Button: 50000[18:17]
类型: 50000
名称: 18:17
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #460
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #461
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #462
路径: Button: 50000[选择目标窗口的内容为空，导致无法选择可监控的内容，怎么解决？]
类型: 50000
名称: 选择目标窗口的内容为空，导致无法选择可监控的内容，怎么解决？
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #463
路径: Button: 50000[选择目标窗口的内容为空，导致无法选择可监控的内容，怎么解决？]
类型: 50000
名称: 选择目标窗口的内容为空，导致无法选择可监控的内容，怎么解决？
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #464
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #465
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #466
路径: Button: 50000[Read lines 42-90 test00.ahk]
类型: 50000
名称: Read lines 42-90 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #467
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #468
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #469
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #470
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #471
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #472
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #473
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #474
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #475
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #476
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #477
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #478
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #479
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #480
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #481
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #482
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #483
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #484
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #485
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #486
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #487
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #488
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #489
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #490
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #491
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #492
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #493
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #494
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #495
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #496
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #497
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #498
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #499
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #500
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #501
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #502
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #503
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #504
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #505
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #506
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #507
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #508
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #509
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #510
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #511
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #512
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #513
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #514
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #515
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #516
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #517
路径: Button: 50000[Checkpoint 4]
类型: 50000
名称: Checkpoint 4
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #518
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #519
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #520
路径: Button: 50000[18:21]
类型: 50000
名称: 18:21
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #521
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #522
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #523
路径: Button: 50000[可以刷新所有窗口了，但是还是在x ]
类型: 50000
名称: 可以刷新所有窗口了，但是还是在x 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #524
路径: Button: 50000[可以刷新所有窗口了，但是还是在x ]
类型: 50000
名称: 可以刷新所有窗口了，但是还是在x 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #525
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #526
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #527
路径: Button: 50000[Pattern Search ScanUIA|StartMouseMonitor test00.ahk]
类型: 50000
名称: Pattern Search ScanUIA|StartMouseMonitor test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #528
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #529
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #530
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #531
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #532
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #533
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #534
路径: Button: 50000[Read lines 151-180 test00.ahk]
类型: 50000
名称: Read lines 151-180 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #535
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #536
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #537
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #538
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #539
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #540
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #541
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #542
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #543
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #544
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #545
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #546
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #547
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #548
路径: Button: 50000[Read lines 213-240 test00.ahk]
类型: 50000
名称: Read lines 213-240 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #549
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #550
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #551
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #552
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #553
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #554
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #555
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #556
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #557
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #558
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #559
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #560
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #561
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #562
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #563
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #564
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #565
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #566
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #567
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #568
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #569
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #570
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #571
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #572
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #573
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #574
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #575
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #576
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #577
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #578
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #579
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #580
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #581
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #582
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #583
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #584
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #585
路径: Button: 50000[Checkpoint 5]
类型: 50000
名称: Checkpoint 5
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #586
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #587
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #588
路径: Button: 50000[10:29]
类型: 50000
名称: 10:29
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #589
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #590
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #591
路径: Button: 50000[根据代码运行情况，如图中所示，可以扫描到一些窗口，但是不准确，有许多窗口是错误的，扫描窗口后，在程序中无法选中窗口导致不能监控插件所在软件的控件信息 ]
类型: 50000
名称: 根据代码运行情况，如图中所示，可以扫描到一些窗口，但是不准确，有许多窗口是错误的，扫描窗口后，在程序中无法选中窗口导致不能监控插件所在软件的控件信息 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #592
路径: Button: 50000[根据代码运行情况，如图中所示，可以扫描到一些窗口，但是不准确，有许多窗口是错误的，扫描窗口后，在程序中无法选中窗口导致不能监控插件所在软件的控件信息 ]
类型: 50000
名称: 根据代码运行情况，如图中所示，可以扫描到一些窗口，但是不准确，有许多窗口是错误的，扫描窗口后，在程序中无法选中窗口导致不能监控插件所在软件的控件信息 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #593
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #594
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #595
路径: Button: 50000[image.png 19KB]
类型: 50000
名称: image.png 19KB
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #596
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #597
路径: Button: 50000[image.png 36KB]
类型: 50000
名称: image.png 36KB
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #598
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #599
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #600
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #601
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #602
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #603
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #604
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #605
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #606
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #607
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #608
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #609
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #610
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #611
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #612
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #613
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #614
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #615
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #616
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #617
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #618
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #619
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #620
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #621
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #622
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #623
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #624
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #625
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #626
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #627
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #628
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #629
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #630
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #631
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #632
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #633
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #634
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #635
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #636
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #637
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #638
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #639
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #640
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #641
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #642
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #643
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #644
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #645
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #646
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #647
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #648
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #649
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #650
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #651
路径: Button: 50000[Checkpoint 6]
类型: 50000
名称: Checkpoint 6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #652
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #653
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #654
路径: Button: 50000[10:36]
类型: 50000
名称: 10:36
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #655
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #656
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #657
路径: Button: 50000[依然是扫描不准确，刷新窗口失败，无法选择所监控的窗口，请查看在整个代码，进行准确无误的修正，实现功能可用化 ]
类型: 50000
名称: 依然是扫描不准确，刷新窗口失败，无法选择所监控的窗口，请查看在整个代码，进行准确无误的修正，实现功能可用化 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #658
路径: Button: 50000[依然是扫描不准确，刷新窗口失败，无法选择所监控的窗口，请查看在整个代码，进行准确无误的修正，实现功能可用化 ]
类型: 50000
名称: 依然是扫描不准确，刷新窗口失败，无法选择所监控的窗口，请查看在整个代码，进行准确无误的修正，实现功能可用化 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #659
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #660
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #661
路径: Button: 50000[image.png 10KB]
类型: 50000
名称: image.png 10KB
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #662
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #663
路径: Button: 50000[image.png 21KB]
类型: 50000
名称: image.png 21KB
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #664
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #665
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #666
路径: Button: 50000[Read lines 1-30 test00.ahk]
类型: 50000
名称: Read lines 1-30 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #667
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #668
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #669
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #670
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #671
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #672
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #673
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #674
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #675
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #676
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #677
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #678
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #679
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #680
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #681
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #682
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #683
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #684
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #685
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #686
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #687
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #688
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #689
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #690
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #691
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #692
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #693
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #694
路径: Button: 50000[Web AutoHotkey v2 ListBox Add method syntax]
类型: 50000
名称: Web AutoHotkey v2 ListBox Add method syntax
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #695
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #696
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #697
路径: Button: 50000[Web Fetch]
类型: 50000
名称: Web Fetch
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #698
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #699
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #700
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #701
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #702
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #703
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #704
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #705
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #706
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #707
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #708
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #709
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #710
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #711
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #712
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #713
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #714
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #715
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #716
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #717
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #718
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #719
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #720
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #721
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #722
路径: Button: 50000[Read lines 22-25 test00.ahk]
类型: 50000
名称: Read lines 22-25 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #723
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #724
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #725
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #726
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #727
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #728
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #729
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #730
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #731
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #732
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #733
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #734
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #735
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #736
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #737
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #738
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #739
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #740
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #741
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #742
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #743
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #744
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #745
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #746
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #747
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #748
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #749
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #750
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #751
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #752
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #753
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #754
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #755
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #756
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #757
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #758
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #759
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #760
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #761
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #762
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #763
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #764
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #765
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #766
路径: Button: 50000[Checkpoint 7]
类型: 50000
名称: Checkpoint 7
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #767
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #768
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #769
路径: Button: 50000[10:46]
类型: 50000
名称: 10:46
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #770
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #771
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #772
路径: Button: 50000[扫描结构失败，无法获取UIA，而且有很多无用的假窗口，不能根据真实的窗口显示吗，比如有俩个窗口是在运行的，那我刷新出来的就是这俩窗口，多余无用的不用添加，按照要求及图片内容进行正确修改 ]
类型: 50000
名称: 扫描结构失败，无法获取UIA，而且有很多无用的假窗口，不能根据真实的窗口显示吗，比如有俩个窗口是在运行的，那我刷新出来的就是这俩窗口，多余无用的不用添加，按照要求及图片内容进行正确修改 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #773
路径: Button: 50000[扫描结构失败，无法获取UIA，而且有很多无用的假窗口，不能根据真实的窗口显示吗，比如有俩个窗口是在运行的，那我刷新出来的就是这俩窗口，多余无用的不用添加，按照要求及图片内容进行正确修改 ]
类型: 50000
名称: 扫描结构失败，无法获取UIA，而且有很多无用的假窗口，不能根据真实的窗口显示吗，比如有俩个窗口是在运行的，那我刷新出来的就是这俩窗口，多余无用的不用添加，按照要求及图片内容进行正确修改 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #774
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #775
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #776
路径: Button: 50000[image.png 32KB]
类型: 50000
名称: image.png 32KB
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #777
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #778
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #779
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #780
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #781
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #782
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #783
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #784
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #785
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #786
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #787
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #788
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #789
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #790
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #791
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #792
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #793
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #794
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #795
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #796
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #797
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #798
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #799
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #800
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #801
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #802
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #803
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #804
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #805
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #806
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #807
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #808
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #809
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #810
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #811
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #812
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #813
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #814
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #815
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #816
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #817
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #818
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #819
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #820
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #821
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #822
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #823
路径: Button: 50000[Checkpoint 8]
类型: 50000
名称: Checkpoint 8
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #824
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #825
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #826
路径: Button: 50000[11:00]
类型: 50000
名称: 11:00
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #827
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #828
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #829
路径: Button: 50000[为什么查看不了运行软件的控件，而且导出信息的时候让结构扫描，但程序上并没有扫描接口，这个开发的程序功能是可以监控并获取运行中的程序状态及他的所有控件信息，包括控件名称，如我在运行vscode，我要我的UIA监控vscode且可以获取vscode的控件信息，按照需要进行代码的修改，完善需求功能 ]
类型: 50000
名称: 为什么查看不了运行软件的控件，而且导出信息的时候让结构扫描，但程序上并没有扫描接口，这个开发的程序功能是可以监控并获取运行中的程序状态及他的所有控件信息，包括控件名称，如我在运行vscode，我要我的UIA监控vscode且可以获取vscode的控件信息，按照需要进行代码的修改，完善需求功能 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #830
路径: Button: 50000[为什么查看不了运行软件的控件，而且导出信息的时候让结构扫描，但程序上并没有扫描接口，这个开发的程序功能是可以监控并获取运行中的程序状态及他的所有控件信息，包括控件名称，如我在运行vscode，我要我的UIA监控vscode且可以获取vscode的控件信息，按照需要进行代码的修改，完善需求功能 ]
类型: 50000
名称: 为什么查看不了运行软件的控件，而且导出信息的时候让结构扫描，但程序上并没有扫描接口，这个开发的程序功能是可以监控并获取运行中的程序状态及他的所有控件信息，包括控件名称，如我在运行vscode，我要我的UIA监控vscode且可以获取vscode的控件信息，按照需要进行代码的修改，完善需求功能 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #831
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #832
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #833
路径: Button: 50000[image.png 6KB]
类型: 50000
名称: image.png 6KB
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #834
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #835
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #836
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #837
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #838
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #839
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #840
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #841
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #842
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #843
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #844
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #845
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #846
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #847
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #848
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #849
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #850
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #851
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #852
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #853
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #854
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #855
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #856
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #857
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #858
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #859
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #860
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #861
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #862
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #863
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #864
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #865
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #866
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #867
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #868
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #869
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #870
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #871
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #872
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #873
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #874
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #875
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #876
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #877
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #878
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #879
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #880
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #881
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #882
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #883
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #884
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #885
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #886
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #887
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #888
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #889
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #890
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #891
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #892
路径: Button: 50000[Pattern Search ExportResult test00.ahk]
类型: 50000
名称: Pattern Search ExportResult test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #893
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #894
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #895
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #896
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #897
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #898
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #899
路径: Button: 50000[Read lines 418-450 test00.ahk]
类型: 50000
名称: Read lines 418-450 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #900
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #901
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #902
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #903
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #904
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #905
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #906
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #907
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #908
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #909
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #910
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #911
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #912
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #913
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #914
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #915
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #916
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #917
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #918
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #919
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #920
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #921
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #922
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #923
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #924
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #925
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #926
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #927
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #928
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #929
路径: Button: 50000[Checkpoint 9]
类型: 50000
名称: Checkpoint 9
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #930
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #931
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #932
路径: Button: 50000[11:18]
类型: 50000
名称: 11:18
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #933
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #934
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #935
路径: Button: 50000[ 为什么扫描结构获得的控件信息只有一个？vscode的每个按键不都是一个控键吗，我想要获取所有控键信息，且获得控键树，怎么实现呢 ]
类型: 50000
名称:  为什么扫描结构获得的控件信息只有一个？vscode的每个按键不都是一个控键吗，我想要获取所有控键信息，且获得控键树，怎么实现呢 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #936
路径: Button: 50000[ 为什么扫描结构获得的控件信息只有一个？vscode的每个按键不都是一个控键吗，我想要获取所有控键信息，且获得控键树，怎么实现呢 ]
类型: 50000
名称:  为什么扫描结构获得的控件信息只有一个？vscode的每个按键不都是一个控键吗，我想要获取所有控键信息，且获得控键树，怎么实现呢 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #937
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #938
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #939
路径: Button: 50000[image.png 8KB]
类型: 50000
名称: image.png 8KB
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #940
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #941
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #942
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #943
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #944
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #945
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #946
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #947
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #948
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #949
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #950
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #951
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #952
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #953
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #954
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #955
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #956
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #957
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #958
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #959
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #960
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #961
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #962
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #963
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #964
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #965
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #966
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #967
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #968
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #969
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #970
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #971
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #972
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #973
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #974
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #975
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #976
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #977
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #978
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #979
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #980
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #981
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #982
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #983
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #984
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #985
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #986
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #987
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #988
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #989
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #990
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #991
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #992
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #993
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #994
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #995
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #996
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #997
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #998
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #999
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1000
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1001
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1002
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1003
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1004
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1005
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1006
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1007
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1008
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1009
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1010
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1011
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1012
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1013
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1014
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1015
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1016
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1017
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1018
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1019
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1020
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1021
路径: Button: 50000[Checkpoint 10]
类型: 50000
名称: Checkpoint 10
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1022
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1023
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1024
路径: Button: 50000[11:25]
类型: 50000
名称: 11:25
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1025
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1026
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1027
路径: Button: 50000[ 仍然只有一个窗口信息，看不到控件信息，这是什么问题？ ]
类型: 50000
名称:  仍然只有一个窗口信息，看不到控件信息，这是什么问题？ 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1028
路径: Button: 50000[ 仍然只有一个窗口信息，看不到控件信息，这是什么问题？ ]
类型: 50000
名称:  仍然只有一个窗口信息，看不到控件信息，这是什么问题？ 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1029
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1030
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1031
路径: Button: 50000[image.png 10KB]
类型: 50000
名称: image.png 10KB
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1032
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1033
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1034
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1035
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1036
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1037
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1038
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1039
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1040
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1041
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1042
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1043
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1044
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1045
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1046
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1047
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1048
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1049
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1050
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1051
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1052
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1053
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1054
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1055
路径: Button: 50000[Read lines 640--1 test00.ahk]
类型: 50000
名称: Read lines 640--1 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1056
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1057
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1058
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1059
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1060
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1061
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1062
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1063
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1064
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1065
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1066
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1067
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1068
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1069
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1070
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1071
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1072
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1073
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1074
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1075
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1076
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1077
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1078
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1079
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1080
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1081
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1082
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1083
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1084
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1085
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1086
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1087
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1088
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1089
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1090
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1091
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1092
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1093
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1094
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1095
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1096
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1097
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1098
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1099
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1100
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1101
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1102
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1103
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1104
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1105
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1106
路径: Button: 50000[Checkpoint 11]
类型: 50000
名称: Checkpoint 11
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1107
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1108
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1109
路径: Button: 50000[11:33]
类型: 50000
名称: 11:33
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1110
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1111
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1112
路径: Button: 50000[无论是vscode还是记事本，UIA都无法获取控件，这是什么问题？ ]
类型: 50000
名称: 无论是vscode还是记事本，UIA都无法获取控件，这是什么问题？ 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1113
路径: Button: 50000[无论是vscode还是记事本，UIA都无法获取控件，这是什么问题？ ]
类型: 50000
名称: 无论是vscode还是记事本，UIA都无法获取控件，这是什么问题？ 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1114
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1115
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1116
路径: Button: 50000[image.png 8KB]
类型: 50000
名称: image.png 8KB
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1117
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1118
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1119
路径: Button: 50000[Read lines 1-5 test00.ahk]
类型: 50000
名称: Read lines 1-5 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1120
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1121
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1122
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1123
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1124
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1125
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1126
路径: Button: 50000[Read directory UIA-v2-1.1.0]
类型: 50000
名称: Read directory UIA-v2-1.1.0
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1127
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1128
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1129
路径: Button: 50000[Read lines 1-30 Example01_Notepad.ahk UIA-v2-1.1.0/Examples]
类型: 50000
名称: Read lines 1-30 Example01_Notepad.ahk UIA-v2-1.1.0/Examples
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1130
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1131
路径: Button: 50000[Example01_Notepad.ahk UIA-v2-1.1.0/Examples]
类型: 50000
名称: Example01_Notepad.ahk UIA-v2-1.1.0/Examples
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1132
路径: Button: 50000[Example01_Notepad.ahk UIA-v2-1.1.0/Examples]
类型: 50000
名称: Example01_Notepad.ahk UIA-v2-1.1.0/Examples
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1133
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1134
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1135
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1136
路径: Button: 50000[Terminal $ .\UIA-v2-1.1.0\Examples\Example01_Notepad.ahk]
类型: 50000
名称: Terminal $ .\UIA-v2-1.1.0\Examples\Example01_Notepad.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1137
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1138
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1139
路径: Button: 50000[Terminal Read from Process]
类型: 50000
名称: Terminal Read from Process
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1140
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1141
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1142
路径: Button: 50000[Terminal Killed Process]
类型: 50000
名称: Terminal Killed Process
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1143
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1144
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1145
路径: Button: 50000[Created file uia_test.ahk]
类型: 50000
名称: Created file uia_test.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1146
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1147
路径: Button: 50000[uia_test.ahk]
类型: 50000
名称: uia_test.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1148
路径: Button: 50000[uia_test.ahk]
类型: 50000
名称: uia_test.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1149
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1150
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1151
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1152
路径: Button: 50000[Terminal $ .\uia_test.ahk]
类型: 50000
名称: Terminal $ .\uia_test.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1153
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1154
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1155
路径: Button: 50000[Created file simple_test.ahk]
类型: 50000
名称: Created file simple_test.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1156
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1157
路径: Button: 50000[simple_test.ahk]
类型: 50000
名称: simple_test.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1158
路径: Button: 50000[simple_test.ahk]
类型: 50000
名称: simple_test.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1159
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1160
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1161
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1162
路径: Button: 50000[Terminal $ .\simple_test.ahk]
类型: 50000
名称: Terminal $ .\simple_test.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1163
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1164
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1165
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1166
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1167
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1168
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1169
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1170
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1171
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1172
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1173
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1174
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1175
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1176
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1177
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1178
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1179
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1180
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1181
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1182
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1183
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1184
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1185
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1186
路径: Button: 50000[Read lines 990--1 test00.ahk]
类型: 50000
名称: Read lines 990--1 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1187
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1188
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1189
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1190
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1191
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1192
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1193
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1194
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1195
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1196
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1197
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1198
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1199
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1200
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1201
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1202
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1203
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1204
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1205
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1206
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1207
路径: Button: 50000[Read lines 697-790 test00.ahk]
类型: 50000
名称: Read lines 697-790 test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1208
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1209
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1210
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1211
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1212
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1213
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1214
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1215
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1216
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1217
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1218
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1219
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1220
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1221
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1222
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1223
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1224
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1225
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1226
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1227
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1228
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1229
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1230
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1231
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1232
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1233
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1234
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1235
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1236
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1237
路径: Button: 50000[Checkpoint 12]
类型: 50000
名称: Checkpoint 12
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1238
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1239
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1240
路径: Button: 50000[11:46]
类型: 50000
名称: 11:46
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1241
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1242
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1243
路径: Button: 50000[以管理员身份运行了，但是获取失败，如图片信息所示 ]
类型: 50000
名称: 以管理员身份运行了，但是获取失败，如图片信息所示 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1244
路径: Button: 50000[以管理员身份运行了，但是获取失败，如图片信息所示 ]
类型: 50000
名称: 以管理员身份运行了，但是获取失败，如图片信息所示 
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1245
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1246
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1247
路径: Button: 50000[image.png 4KB]
类型: 50000
名称: image.png 4KB
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1248
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1249
路径: Button: 50000[Read lines 1-50 UIA.ahk UIA-v2-1.1.0/Lib]
类型: 50000
名称: Read lines 1-50 UIA.ahk UIA-v2-1.1.0/Lib
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1250
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1251
路径: Button: 50000[UIA.ahk UIA-v2-1.1.0/Lib]
类型: 50000
名称: UIA.ahk UIA-v2-1.1.0/Lib
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1252
路径: Button: 50000[UIA.ahk UIA-v2-1.1.0/Lib]
类型: 50000
名称: UIA.ahk UIA-v2-1.1.0/Lib
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1253
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1254
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1255
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1256
路径: Button: 50000[Pattern Search FindAll UIA.ahk UIA-v2-1.1.0/Lib]
类型: 50000
名称: Pattern Search FindAll UIA.ahk UIA-v2-1.1.0/Lib
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1257
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1258
路径: Button: 50000[UIA.ahk UIA-v2-1.1.0/Lib]
类型: 50000
名称: UIA.ahk UIA-v2-1.1.0/Lib
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1259
路径: Button: 50000[UIA.ahk UIA-v2-1.1.0/Lib]
类型: 50000
名称: UIA.ahk UIA-v2-1.1.0/Lib
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1260
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1261
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1262
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1263
路径: Button: 50000[Edited file test00.ahk +6 -6]
类型: 50000
名称: Edited file test00.ahk +6 -6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1264
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1265
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1266
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1267
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1268
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1269
路径: Button: 50000[+6]
类型: 50000
名称: +6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1270
路径: Button: 50000[+6]
类型: 50000
名称: +6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1271
路径: Button: 50000[-6]
类型: 50000
名称: -6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1272
路径: Button: 50000[-6]
类型: 50000
名称: -6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1273
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1274
路径: Button: 50000[Edited file test00.ahk +6 -6]
类型: 50000
名称: Edited file test00.ahk +6 -6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1275
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1276
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1277
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1278
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1279
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1280
路径: Button: 50000[+6]
类型: 50000
名称: +6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1281
路径: Button: 50000[+6]
类型: 50000
名称: +6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1282
路径: Button: 50000[-6]
类型: 50000
名称: -6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1283
路径: Button: 50000[-6]
类型: 50000
名称: -6
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1284
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1285
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1286
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1287
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1288
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1289
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1290
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1291
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1292
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1293
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1294
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1295
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1296
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1297
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1298
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1299
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1300
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1301
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1302
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1303
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1304
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1305
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1306
路径: Button: 50000[Edited file test00.ahk]
类型: 50000
名称: Edited file test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1307
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1308
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1309
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1310
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1311
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1312
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1313
路径: Button: 50000[TraverseElement]
类型: 50000
名称: TraverseElement
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1314
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1315
路径: Button: 50000[Feedback Positive]
类型: 50000
名称: Feedback Positive
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1316
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1317
路径: Button: 50000[Feedback Negative]
类型: 50000
名称: Feedback Negative
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1318
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1319
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1320
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1321
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1322
路径: Button: 50000[More options]
类型: 50000
名称: More options
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1323
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1324
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1325
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1326
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1327
路径: Button: 50000[+1101]
类型: 50000
名称: +1101
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1328
路径: Button: 50000[-384]
类型: 50000
名称: -384
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1329
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1330
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1331
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1332
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1333
路径: Button: 50000[Drop files to attach as context images (png, jpg, jpeg) Auto Auto Toggle between manual and auto agent mode project]
类型: 50000
名称: Drop files to attach as context images (png, jpg, jpeg) Auto Auto Toggle between manual and auto agent mode project
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1334
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1335
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1336
路径: Button: 50000[Drop files to attach as context images (png, jpg, jpeg)]
类型: 50000
名称: Drop files to attach as context images (png, jpg, jpeg)
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1337
路径: Button: 50000[Auto Auto Toggle between manual and auto agent mode]
类型: 50000
名称: Auto Auto Toggle between manual and auto agent mode
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1338
路径: Button: 50000[Toggle between manual and auto agent mode]
类型: 50000
名称: Toggle between manual and auto agent mode
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1339
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1340
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1341
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1342
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1343
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1344
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1345
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1346
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1347
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1348
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1349
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1350
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1351
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1352
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1353
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1354
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1355
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1356
路径: Button: 50000[project Remove source folder from context]
类型: 50000
名称: project Remove source folder from context
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1357
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1358
路径: Button: 50000[project]
类型: 50000
名称: project
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1359
路径: Button: 50000[Remove source folder from context]
类型: 50000
名称: Remove source folder from context
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1360
路径: Button: 50000[test00.ahk Remove file from context]
类型: 50000
名称: test00.ahk Remove file from context
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1361
路径: Button: 50000
类型: 50000
名称: [无名称]
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1362
路径: Button: 50000[test00.ahk]
类型: 50000
名称: test00.ahk
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1363
路径: Button: 50000[Remove file from context]
类型: 50000
名称: Remove file from context
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1364
路径: Button: 50000[Expand context]
类型: 50000
名称: Expand context
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1365
路径: Button: 50000[最小化]
类型: 50000
名称: 最小化
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1366
路径: Button: 50000[恢复]
类型: 50000
名称: 恢复
值: [无值]
自动化ID: [无ID]
---------------------------------------------------
控件 #1367
路径: Button: 50000[关闭]
类型: 50000
名称: 关闭
值: [无值]
自动化ID: [无ID]
---------------------------------------------------

=== 报告结束 ===
