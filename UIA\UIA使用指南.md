# UIA-v2 使用指南

## 📖 简介

UIA-v2是一个AutoHotkey v2的UI自动化库，基于微软的UI Automation框架。它可以自动化那些传统AutoHotkey难以处理的现代应用程序，如UWP应用、现代浏览器、Office应用等。

## 🚀 快速开始

### 1. 引入库文件

```autohotkey
#Requires AutoHotkey v2
#include UIA-v2-1.1.0\Lib\UIA.ahk
```

### 2. 基本使用流程

1. **获取窗口元素**：`UIA.ElementFromHandle()`
2. **查找目标元素**：`FindElement()` 或 `FindElements()`
3. **操作元素**：设置值、点击、获取信息等

## 🔍 查找元素的方法

### 基本查找条件

```autohotkey
; 按类型查找
element := windowEl.FindElement({Type:"Button"})

; 按名称查找
element := windowEl.FindElement({Name:"确定"})

; 按AutomationId查找
element := windowEl.FindElement({AutomationId:"btnOK"})

; 组合条件（AND）
element := windowEl.FindElement({Type:"Button", Name:"确定"})
```

### 高级查找条件

```autohotkey
; OR条件 - 查找按钮或链接
element := windowEl.FindElement([{Type:"Button"}, {Type:"Hyperlink"}])

; 部分匹配
element := windowEl.FindElement({Name:"确定", matchmode:"Substring"})

; 不区分大小写
element := windowEl.FindElement({Name:"button", casesense:0})

; NOT条件
element := windowEl.FindElement({Type:"Button", not:{Name:"取消"}})

; 查找第N个元素
element := windowEl.FindElement({Type:"Button", index:3})
```

## 🎯 常用元素类型

| 类型 | 说明 | 示例 |
|------|------|------|
| Button | 按钮 | 确定、取消按钮 |
| Edit | 文本框 | 输入框、文本区域 |
| Text | 文本标签 | 静态文本 |
| MenuItem | 菜单项 | 文件、编辑菜单 |
| Document | 文档区域 | 记事本编辑区 |
| Hyperlink | 超链接 | 网页链接 |
| Image | 图片 | 图标、图片 |
| List | 列表 | 文件列表 |
| Tree | 树形控件 | 文件夹树 |
| Tab | 标签页 | 浏览器标签 |

## 🛠️ 常用操作方法

### 元素信息获取

```autohotkey
; 获取元素属性
name := element.Name              ; 元素名称
type := element.Type              ; 元素类型
value := element.Value            ; 元素值
rect := element.BoundingRectangle ; 元素位置和大小

; 检查元素状态
isEnabled := element.IsEnabled    ; 是否启用
isVisible := element.IsVisible    ; 是否可见
```

### 元素操作

```autohotkey
; 点击元素
element.Click()

; 设置焦点
element.SetFocus()

; 设置值（适用于文本框）
element.Value := "新文本"

; 高亮显示（调试用）
element.Highlight()

; 获取元素详细信息
info := element.Dump()
```

### 等待元素

```autohotkey
; 等待元素出现（最多等待5秒）
element := windowEl.WaitElement({Type:"Button", Name:"确定"}, 5000)
```

## 🌐 浏览器自动化

UIA-v2特别适合浏览器自动化，还提供了专门的浏览器库：

```autohotkey
#include UIA-v2-1.1.0\Lib\UIA_Browser.ahk

; 获取Chrome浏览器
chrome := UIA_Browser("chrome.exe")

; 导航到网页
chrome.Navigate("https://www.example.com")

; 查找并点击链接
link := chrome.FindElement({Type:"Hyperlink", Name:"点击这里"})
link.Click()
```

## 🔧 实用工具

### UIA树形查看器

运行 `UIATreeInspector.ahk` 可以查看应用程序的UIA元素树结构，这对于调试和查找元素非常有用。

### 元素高亮

在开发过程中，使用 `element.Highlight()` 可以高亮显示找到的元素，确保找到了正确的目标。

## 📝 最佳实践

### 1. 错误处理

```autohotkey
try {
    element := windowEl.FindElement({Type:"Button", Name:"确定"})
    element.Click()
} catch Error as e {
    MsgBox("操作失败: " . e.Message)
}
```

### 2. 等待策略

对于动态加载的内容，使用 `WaitElement()` 而不是 `FindElement()`：

```autohotkey
; 好的做法
element := windowEl.WaitElement({Type:"Button"}, 5000)

; 避免这样做
Sleep(2000)  ; 固定等待时间不可靠
element := windowEl.FindElement({Type:"Button"})
```

### 3. 元素缓存

如果需要多次操作同一个元素，先将其存储在变量中：

```autohotkey
textBox := windowEl.FindElement({Type:"Edit"})
textBox.SetFocus()
textBox.Value := "文本1"
Sleep(1000)
textBox.Value := "文本2"
```

## 🚨 常见问题

### 1. 找不到元素

- 使用UIATreeInspector查看元素结构
- 检查元素是否真的存在和可见
- 尝试不同的查找条件
- 使用WaitElement等待元素加载

### 2. 权限问题

- 以管理员身份运行脚本
- 确保目标应用程序支持UIA

### 3. 性能问题

- 避免过于宽泛的查找条件
- 使用具体的查找条件（如AutomationId）
- 缓存经常使用的元素

## 📚 更多资源

- [官方Wiki](https://github.com/Descolada/UIA-v2/wiki)
- [示例文件夹](UIA-v2-1.1.0/Examples/)
- [AutoHotkey论坛讨论](https://www.autohotkey.com/boards/viewtopic.php?f=83&t=113065)

## 🎯 总结

UIA-v2是一个强大的UI自动化工具，特别适合：

- 现代Windows应用程序
- 浏览器自动化
- Office应用程序
- UWP应用程序
- 传统AutoHotkey难以处理的界面

通过合理使用查找条件和操作方法，可以实现复杂的UI自动化任务。
