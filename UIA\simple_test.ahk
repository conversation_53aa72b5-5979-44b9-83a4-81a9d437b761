#Requires AutoHotkey v2.0

; 不使用UIA的简单测试
MsgBox("AutoHotkey v2.0 基本测试")

; 测试基本窗口操作
try {
    windowList := WinGetList()
    MsgBox("找到 " . windowList.Length . " 个窗口")
    
    ; 显示前5个窗口
    info := "前5个窗口:`n"
    maxShow := Min(windowList.Length, 5)
    Loop maxShow {
        hwnd := windowList[A_Index]
        title := WinGetTitle(hwnd)
        className := WinGetClass(hwnd)
        info .= A_Index . ". " . title . " [" . className . "]`n"
    }
    MsgBox(info)
    
} catch as e {
    MsgBox("基本测试失败: " . e.message)
}

MsgBox("基本测试完成")
ExitApp
