# 通用UI控件检查器使用说明

## 概述
这是一个基于AutoHotkey v2和UIA v2库开发的通用桌面应用程序UI控件检查器，可以获取任意桌面应用程序的完整控件树结构。

## 主要功能

### 1. 基础功能
- **应用程序扫描**：自动检测并列出所有运行的桌面应用程序
- **控件树获取**：递归获取应用程序的完整UI控件层次结构
- **详细信息显示**：显示每个控件的名称、类型、类名、位置、状态等详细信息
- **结果导出**：将扫描结果导出为文本文件

### 2. 高级功能
- **搜索控件**：在扫描结果中搜索特定的控件
- **实时监控**：实时显示鼠标位置处的控件信息
- **统计报告**：生成控件类型分布统计报告
- **多级扫描**：支持浅层、中等、深层、完整四种扫描深度

## 使用方法

### 1. 启动程序
```
运行 Universal_UI_Inspector.ahk
```

### 2. 选择目标应用程序
1. 程序启动后会自动刷新应用程序列表
2. 在列表中选择要分析的应用程序
3. 双击或点击"扫描控件"按钮开始分析

### 3. 查看结果
- 扫描完成后，控件树结构会显示在下方的文本框中
- 每个控件都会显示其层次关系和详细属性信息

### 4. 导出结果
- 点击"导出结果"按钮将结果保存为文本文件
- 文件名格式：`UI_Controls_应用程序名_时间戳.txt`

## 界面说明

### 主界面控件
- **应用程序列表**：显示所有可用的桌面应用程序
- **控制按钮区域**：
  - 刷新列表：重新扫描运行的应用程序
  - 扫描控件：开始分析选中的应用程序
  - 导出结果：保存扫描结果到文件
  - 清空结果：清空显示区域
- **高级功能按钮**：
  - 搜索控件：在结果中搜索特定控件
  - 实时监控：启动鼠标位置控件监控
  - 统计报告：生成控件统计分析

### 扫描选项
- **扫描深度**：
  - 浅层(3级)：只扫描前3层控件，速度快
  - 中等(5级)：扫描前5层控件，平衡性能和详细度
  - 深层(8级)：扫描前8层控件，较详细
  - 完整(无限)：扫描所有层级，最详细但可能较慢

- **过滤系统控件**：过滤掉一些系统级别的控件，减少干扰

## 热键说明
- **F1**：刷新应用程序列表
- **F2**：扫描选中应用程序的控件
- **F3**：导出扫描结果
- **F4**：清空结果显示
- **F5**：搜索控件
- **F6**：启动实时监控
- **F7**：生成统计报告
- **F12**：退出程序

## 高级功能详解

### 1. 搜索控件功能
- 在扫描结果中搜索包含特定关键词的控件
- 支持按控件名称、类型、类名等搜索
- 搜索结果会在新窗口中显示

### 2. 实时监控功能
- 实时显示鼠标位置处的控件信息
- 只监控选中的目标应用程序窗口
- 显示控件的详细属性和位置信息
- 可以用于动态分析应用程序的UI结构

### 3. 统计报告功能
- 分析控件类型分布
- 统计各种控件的数量和占比
- 生成详细的统计报告
- 支持导出统计结果

## 输出格式说明

### 控件树结构格式
```
├─ 控件类型: 控件名称
│  类名: QWidget
│  自动化ID: MainWindow
│  位置: (100,50) 大小: 800x600
│  启用: 是
│  可见: 是
│  支持模式: Invoke, Selection
  ├─ 子控件类型: 子控件名称
  │  ...
```

### 统计报告格式
```
=== 应用程序名 控件统计报告 ===
生成时间: 2025-07-31 12:00:00
总控件数量: 156

=== 控件类型分布 ===
按钮: 45 个 (28.8%)
文本: 32 个 (20.5%)
编辑框: 18 个 (11.5%)
...
```

## 注意事项

1. **权限要求**：某些应用程序可能需要管理员权限才能完全访问其UI结构
2. **性能考虑**：对于复杂的应用程序，完整扫描可能需要较长时间
3. **兼容性**：主要支持标准的Windows应用程序，对于某些特殊应用可能支持有限
4. **UIA支持**：目标应用程序需要支持UI Automation才能获取详细信息

## 故障排除

### 常见问题
1. **无法获取控件信息**：
   - 确保目标应用程序支持UI Automation
   - 尝试以管理员权限运行检查器
   - 检查应用程序是否被安全软件保护

2. **扫描速度慢**：
   - 降低扫描深度
   - 启用"过滤系统控件"选项
   - 关闭不必要的应用程序

3. **结果显示不完整**：
   - 增加扫描深度
   - 禁用过滤选项
   - 确保目标应用程序窗口完全可见

## 技术说明

### 依赖库
- AutoHotkey v2
- UIA v2库 (UI Automation库)

### 支持的控件类型
- 按钮 (Button)
- 文本 (Text)
- 编辑框 (Edit)
- 列表 (List)
- 树形控件 (Tree)
- 菜单 (Menu)
- 工具栏 (ToolBar)
- 选项卡 (Tab)
- 复选框 (CheckBox)
- 单选按钮 (RadioButton)
- 组合框 (ComboBox)
- 滑块 (Slider)
- 进度条 (ProgressBar)
- 等等...

### 获取的控件属性
- 名称 (Name)
- 类型 (LocalizedType)
- 类名 (ClassName)
- 自动化ID (AutomationId)
- 值 (Value)
- 位置和大小 (BoundingRectangle)
- 启用状态 (IsEnabled)
- 可见状态 (IsVisible)
- 支持的操作模式 (SupportedPatterns)

## 更新日志

### v1.0 (2025-07-31)
- 初始版本发布
- 基础控件扫描功能
- 应用程序列表和选择
- 结果导出功能
- 多级扫描深度支持
- 搜索和实时监控功能
- 统计报告生成

---

如有问题或建议，请联系开发者。
