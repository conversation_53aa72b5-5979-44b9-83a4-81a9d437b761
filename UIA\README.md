# AutoHotkey 网页生成器使用指南

## 项目简介

本项目包含了使用AutoHotkey v2创建的网页生成器工具，可以快速生成各种类型的现代化网页。通过简单的热键操作，即可生成完整的HTML页面并在浏览器中预览。

## 文件说明

### 1. `simple-webpage.ahk` - 图形界面网页编辑器
- **功能**：提供完整的GUI界面，可以编辑HTML代码并实时预览
- **特点**：
  - 可视化编辑界面
  - 实时预览功能
  - 文件保存和打开
  - 内置默认模板
  - 支持窗口大小调整

### 2. `quick-webpage.ahk` - 快速网页生成器
- **功能**：通过热键快速生成预设的网页模板
- **特点**：
  - 一键生成多种网页类型
  - 自动在浏览器中打开
  - 无需手动编写代码
  - 包含现代化设计

### 3. `test01.ahk` - 网站快捷访问工具
- **功能**：使用热键快速打开常用网站
- **特点**：
  - 支持多个网站快捷访问
  - 智能浏览器选择
  - 批量打开网站功能

## 使用方法

### 图形界面编辑器 (`simple-webpage.ahk`)

1. **启动程序**：双击运行 `simple-webpage.ahk`
2. **编辑HTML**：在编辑框中输入或修改HTML代码
3. **预览网页**：点击"预览网页"按钮在浏览器中查看效果
4. **保存文件**：点击"保存HTML"按钮将代码保存为HTML文件
5. **打开文件**：点击"打开文件"按钮加载现有的HTML文件

**快捷键**：
- `Ctrl+S`：快速保存
- `F5`：快速预览
- `Ctrl+N`：清空内容

### 快速生成器 (`quick-webpage.ahk`)

1. **启动程序**：双击运行 `quick-webpage.ahk`
2. **使用热键生成网页**：
   - `F1`：生成个人介绍页面
   - `F2`：生成产品展示页面
   - `F3`：生成登录表单页面
   - `F4`：生成图片画廊页面
   - `F5`：显示帮助信息
   - `ESC`：退出程序

## 生成的网页类型详解

### 1. 个人介绍页面 (F1)
- **文件名**：`personal_profile.html`
- **内容包含**：
  - 个人头像和基本信息
  - 技能标签展示
  - 工作经历介绍
  - 联系方式展示
- **特色功能**：
  - 响应式设计
  - 鼠标悬停动画效果
  - 页面加载动画
  - 现代化卡片布局

### 2. 产品展示页面 (F2)
- **文件名**：`product_showcase.html`
- **内容包含**：
  - 产品英雄区域
  - 特色功能展示
  - 技术规格表格
  - 价格和购买按钮
- **特色功能**：
  - 渐变背景设计
  - 平滑滚动效果
  - 交互式按钮
  - 网格布局系统

### 3. 登录表单页面 (F3)
- **文件名**：`login_form.html`
- **内容包含**：
  - 用户名/密码输入框
  - 记住密码选项
  - 社交登录按钮
  - 注册链接
- **特色功能**：
  - 表单验证功能
  - 错误消息提示
  - 输入框焦点效果
  - 模拟登录过程
- **演示账号**：用户名 `admin`，密码 `123456`

### 4. 图片画廊页面 (F4)
- **文件名**：`image_gallery.html`
- **内容包含**：
  - 分类过滤标签
  - 响应式图片网格
  - 图片详情模态框
  - 图片导航功能
- **特色功能**：
  - 图片懒加载
  - 分类过滤系统
  - 全屏查看模式
  - 键盘导航支持

## 技术特点

### CSS 特性
- **现代化设计**：使用渐变、阴影、圆角等现代CSS特性
- **响应式布局**：支持各种屏幕尺寸的设备
- **动画效果**：包含过渡动画和交互反馈
- **Flexbox/Grid**：使用现代布局技术

### JavaScript 功能
- **交互效果**：鼠标悬停、点击等交互响应
- **表单验证**：客户端表单验证和错误处理
- **动态内容**：动态生成和更新页面内容
- **事件处理**：键盘、鼠标事件的完整支持

### AutoHotkey 集成
- **文件操作**：自动创建和保存HTML文件
- **浏览器启动**：自动在默认浏览器中打开生成的页面
- **错误处理**：完善的错误捕获和用户提示
- **用户界面**：直观的GUI界面和热键操作

## 自定义和扩展

### 修改模板内容
1. 打开对应的 `.ahk` 文件
2. 找到相应的HTML模板字符串
3. 修改HTML、CSS或JavaScript代码
4. 保存文件并重新运行

### 添加新的网页类型
1. 在 `quick-webpage.ahk` 中添加新的热键定义
2. 创建对应的生成函数
3. 编写HTML模板内容
4. 更新帮助信息

### 自定义样式
- 修改CSS部分的颜色、字体、布局等
- 调整响应式断点和网格系统
- 添加新的动画效果和交互

## 系统要求

- **操作系统**：Windows 7/8/10/11
- **AutoHotkey版本**：v2.0 或更高版本
- **浏览器**：任何现代浏览器（Chrome、Firefox、Edge等）

## 安装和运行

1. **安装AutoHotkey v2**：
   - 访问 [AutoHotkey官网](https://www.autohotkey.com/)
   - 下载并安装AutoHotkey v2版本

2. **运行脚本**：
   - 双击 `.ahk` 文件直接运行
   - 或右键选择"Compile Script"编译为exe文件

3. **查看生成的网页**：
   - 生成的HTML文件保存在脚本同目录下
   - 自动在默认浏览器中打开预览

## 常见问题

### Q: 为什么生成的网页无法正常显示？
A: 请确保：
- 使用现代浏览器
- 网络连接正常（某些图片来自网络）
- HTML文件编码为UTF-8

### Q: 如何修改生成的网页内容？
A: 可以：
- 使用图形界面编辑器修改
- 直接编辑生成的HTML文件
- 修改脚本中的模板内容

### Q: 能否添加更多网页模板？
A: 可以通过修改脚本代码添加新的模板，参考现有模板的结构即可。

## 版本历史

- **v1.0**：初始版本，包含基本的网页生成功能
- 支持4种网页类型
- 图形界面编辑器
- 快速热键生成

## 许可证

本项目仅供学习和个人使用，请勿用于商业用途。

## 联系方式

如有问题或建议，欢迎反馈交流。
