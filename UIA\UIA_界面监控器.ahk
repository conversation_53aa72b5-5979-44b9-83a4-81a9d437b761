#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

/*
界面变化监控器
功能：
1. 监控窗口焦点变化
2. 监控元素属性变化
3. 监控结构变化（元素添加/删除）
4. 监控文本内容变化
5. 实时日志记录
*/

; 全局变量
global 监控窗口
global 日志列表
global 状态标签
global 监控状态 := false
global 事件处理器组
global 当前监控目标 := ""
global 目标元素

; 创建监控界面
创建监控界面() {
    global 监控窗口 := Gui("+Resize", "界面变化监控器")
    监控窗口.MarginX := 10
    监控窗口.MarginY := 10
    
    ; 标题
    监控窗口.Add("Text", "x10 y10 w500 Center", "界面变化监控器").SetFont("s14 Bold")
    
    ; 监控目标选择
    监控窗口.Add("Text", "x10 y45 w100", "监控目标:")
    目标输入框 := 监控窗口.Add("Edit", "x120 y38 w200 vTargetInput")
    目标输入框.Text := "ahk_exe chrome.exe"
    
    ; 监控类型选择
    监控窗口.Add("Text", "x10 y75 w100", "监控类型:")
    焦点监控 := 监控窗口.Add("Checkbox", "x120 y70 w100 Checked v焦点监控", "焦点变化")
    属性监控 := 监控窗口.Add("Checkbox", "x230 y70 w100 Checked v属性监控", "属性变化")
    结构监控 := 监控窗口.Add("Checkbox", "x340 y70 w100 Checked v结构监控", "结构变化")
    文本监控 := 监控窗口.Add("Checkbox", "x450 y70 w100 Checked v文本监控", "文本变化")
    
    ; 控制按钮
    开始按钮 := 监控窗口.Add("Button", "x10 y100 w80 h30", "开始监控")
    停止按钮 := 监控窗口.Add("Button", "x100 y100 w80 h30", "停止监控")
    清空按钮 := 监控窗口.Add("Button", "x190 y100 w80 h30", "清空日志")
    测试按钮 := 监控窗口.Add("Button", "x280 y100 w80 h30", "测试连接")
    
    ; 状态显示
    global 状态标签 := 监控窗口.Add("Text", "x370 y105 w200", "状态: 未开始")
    状态标签.SetFont("s10 Bold")
    
    ; 日志显示区域
    监控窗口.Add("Text", "x10 y140 w100", "监控日志:")
    global 日志列表 := 监控窗口.Add("Edit", "x10 y160 w560 h300 VScroll ReadOnly")
    
    ; 事件绑定
    开始按钮.OnEvent("Click", 开始监控)
    停止按钮.OnEvent("Click", 停止监控)
    清空按钮.OnEvent("Click", 清空日志)
    测试按钮.OnEvent("Click", 测试连接)

    ; 窗口事件
    监控窗口.OnEvent("Close", (*) => ExitApp())
    监控窗口.Show("w580 h480")

    ; 添加调试信息（在窗口显示后）
    添加日志("界面已创建，按钮事件已绑定")
}

; 开始监控
开始监控(*) {
    global 监控状态, 事件处理器组, 当前监控目标, 目标元素

    添加日志("🚀 开始监控按钮被点击")

    if (监控状态) {
        添加日志("监控已在运行中...")
        return
    }
    
    try {
        ; 获取监控目标
        当前监控目标 := 监控窗口["TargetInput"].Text
        if (当前监控目标 = "") {
            MsgBox("请输入监控目标！")
            return
        }
        
        ; 检查目标是否存在
        if (!WinExist(当前监控目标)) {
            MsgBox("目标窗口不存在，请先启动目标应用程序！")
            return
        }
        
        ; 获取目标元素
        目标元素 := UIA.ElementFromHandle(当前监控目标)
        
        ; 创建事件处理器组
        事件处理器组 := UIA.CreateEventHandlerGroup()
        
        ; 注册不同类型的事件监听器
        注册事件监听器(目标元素)
        
        监控状态 := true
        状态标签.Text := "状态: 监控中..."
        状态标签.SetFont("s10 Bold", "Green")
        添加日志("开始监控目标: " . 当前监控目标)
        
    } catch Error as e {
        MsgBox("启动监控失败: " . e.Message)
        添加日志("启动监控失败: " . e.Message)
    }
}

; 停止监控
停止监控(*) {
    global 监控状态, 事件处理器组, 目标元素

    添加日志("🛑 停止监控按钮被点击")

    if (!监控状态) {
        添加日志("监控未在运行...")
        return
    }
    
    try {
        ; 移除事件处理器
        if (事件处理器组 && 目标元素) {
            UIA.RemoveEventHandlerGroup(事件处理器组, 目标元素)
            事件处理器组 := ""
            目标元素 := ""
        }
        
        监控状态 := false
        状态标签.Text := "状态: 已停止"
        状态标签.SetFont("s10 Bold", "Red")
        添加日志("监控已停止")
        
    } catch Error as e {
        MsgBox("停止监控失败: " . e.Message)
        添加日志("停止监控失败: " . e.Message)
    }
}

; 注册事件监听器
注册事件监听器(目标元素) {
    global 事件处理器组
    
    try {
        ; 1. 焦点变化事件
        if (监控窗口["焦点监控"].Value) {
            焦点事件处理器 := UIA.CreateFocusChangedEventHandler(焦点变化回调)
            UIA.AddFocusChangedEventHandler(焦点事件处理器)
            添加日志("已注册焦点变化监控")
        }

        ; 2. 属性变化事件
        if (监控窗口["属性监控"].Value) {
            属性事件处理器 := UIA.CreatePropertyChangedEventHandler(属性变化回调)
            UIA.AddPropertyChangedEventHandler(属性事件处理器, 目标元素, [UIA.Property.Name, UIA.Property.Value], UIA.TreeScope.Descendants)
            添加日志("已注册属性变化监控")
        }

        ; 3. 结构变化事件
        if (监控窗口["结构监控"].Value) {
            结构事件处理器 := UIA.CreateStructureChangedEventHandler(结构变化回调)
            UIA.AddStructureChangedEventHandler(结构事件处理器, 目标元素, UIA.TreeScope.Subtree)
            添加日志("已注册结构变化监控")
        }

        ; 4. 文本变化事件（通过属性变化监控Value属性）
        if (监控窗口["文本监控"].Value) {
            添加日志("文本变化监控已包含在属性监控中")
        }
        
    } catch Error as e {
        添加日志("注册事件监听器失败: " . e.Message)
    }
}

; 焦点变化回调函数
焦点变化回调(sender) {
    try {
        元素名称 := sender.Name ? sender.Name : "未知元素"
        元素类型 := sender.Type ? sender.Type : "未知类型"
        添加日志("🔍 焦点变化: " . 元素类型 . " - " . 元素名称)
    } catch {
        添加日志("🔍 焦点变化: 无法获取元素信息")
    }
}

; 属性变化回调函数
属性变化回调(sender, propertyId, newValue) {
    try {
        元素名称 := sender.Name ? sender.Name : "未知元素"
        新值 := newValue ? newValue : "空值"

        ; 判断属性类型
        属性名称 := "未知属性"
        if (propertyId = UIA.Property.Name)
            属性名称 := "名称"
        else if (propertyId = UIA.Property.Value)
            属性名称 := "值"

        添加日志("📝 属性变化: " . 元素名称 . " - " . 属性名称 . " = " . 新值)
    } catch {
        添加日志("📝 属性变化: 无法获取变化信息")
    }
}

; 结构变化回调函数
结构变化回调(sender, changeType, runtimeId) {
    try {
        元素名称 := sender.Name ? sender.Name : "未知元素"

        变化描述 := "未知变化"
        switch changeType {
            case 0: 变化描述 := "子元素添加"
            case 1: 变化描述 := "子元素移除"
            case 2: 变化描述 := "子元素无效"
            case 3: 变化描述 := "子元素重新排序"
        }

        添加日志("🏗️ 结构变化: " . 元素名称 . " - " . 变化描述)
    } catch {
        添加日志("🏗️ 结构变化: 无法获取变化信息")
    }
}

; 测试连接
测试连接(*) {
    添加日志("🔧 测试连接按钮被点击")
    try {
        目标 := 监控窗口["TargetInput"].Text
        if (目标 = "") {
            MsgBox("请输入监控目标！")
            return
        }
        
        if (!WinExist(目标)) {
            MsgBox("❌ 目标窗口不存在！")
            添加日志("测试失败: 目标窗口不存在")
            return
        }
        
        ; 尝试获取UI元素
        元素 := UIA.ElementFromHandle(目标)
        元素名称 := 元素.Name ? 元素.Name : "未知窗口"
        
        MsgBox("✅ 连接成功！`n窗口名称: " . 元素名称)
        添加日志("测试成功: 已连接到 " . 元素名称)
        
    } catch Error as e {
        MsgBox("❌ 连接失败: " . e.Message)
        添加日志("测试失败: " . e.Message)
    }
}

; 清空日志
清空日志(*) {
    添加日志("🧹 清空日志按钮被点击")
    日志列表.Text := ""
    添加日志("日志已清空")
}

; 添加日志
添加日志(消息) {
    时间戳 := FormatTime(A_Now, "HH:mm:ss")
    新日志 := "[" . 时间戳 . "] " . 消息 . "`r`n"
    日志列表.Text .= 新日志
    
    ; 自动滚动到底部
    日志列表.Focus()
    Send("^{End}")
}

; 启动监控界面
创建监控界面()
