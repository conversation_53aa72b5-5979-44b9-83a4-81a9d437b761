﻿=== CoppeliaSim 增强监控日志 ===
监控开始时间: 2025-07-31 18:06:19

时间: 18:06:28
操作: 系统
元素: 监控启动
脚本: monitor.start()
详情: 增强监控系统已启动
---
时间: 18:06:28
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:06:31
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:06:32
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:06:33
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:06:34
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:06:35
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:06:36
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:06:37
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:06:38
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:06:40
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:06:41
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:06:44
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:06:46
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:06:47
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:06:49
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:06:53
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.1 fps)')
详情: 当前场景：rendering: 2 ms (8.1 fps)
---
时间: 18:06:54
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:06:55
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:06:58
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:06:59
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.1 fps)')
详情: 当前场景：rendering: 2 ms (8.1 fps)
---
时间: 18:07:00
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:01
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:03
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:06
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:07
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.1 fps)')
详情: 当前场景：rendering: 2 ms (8.1 fps)
---
时间: 18:07:08
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:09
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.1 fps)')
详情: 当前场景：rendering: 2 ms (8.1 fps)
---
时间: 18:07:10
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:11
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:12
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:16
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.1 fps)')
详情: 当前场景：rendering: 2 ms (8.1 fps)
---
时间: 18:07:17
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:18
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:19
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:20
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:21
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:26
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.1 fps)')
详情: 当前场景：rendering: 2 ms (8.1 fps)
---
时间: 18:07:27
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:29
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.1 fps)')
详情: 当前场景：rendering: 2 ms (8.1 fps)
---
时间: 18:07:30
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:33
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:34
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:36
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:37
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:39
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:41
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.1 fps)')
详情: 当前场景：rendering: 2 ms (8.1 fps)
---
时间: 18:07:42
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:46
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:47
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:51
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.1 fps)')
详情: 当前场景：rendering: 2 ms (8.1 fps)
---
时间: 18:07:52
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:54
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:55
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:07:58
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
时间: 18:07:59
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.1 fps)')
详情: 当前场景：rendering: 2 ms (8.1 fps)
---
时间: 18:08:00
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (8.0 fps)')
详情: 当前场景：rendering: 2 ms (8.0 fps)
---
时间: 18:08:01
操作: 场景管理
元素: 场景切换
脚本: sim.loadScene('rendering: 2 ms (7.9 fps)')
详情: 当前场景：rendering: 2 ms (7.9 fps)
---
